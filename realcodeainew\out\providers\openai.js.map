{"version": 3, "file": "openai.js", "sourceRoot": "", "sources": ["../../src/providers/openai.ts"], "names": [], "mappings": ";;;;;;AAAA,oDAA4B;AAC5B,iCAAwC;AAGxC,MAAa,cAAe,SAAQ,qBAAc;IAChD,IAAI,GAAG,QAAQ,CAAC;IAChB,MAAM,GAAc;QAClB;YACE,EAAE,EAAE,aAAa;YACjB,IAAI,EAAE,OAAO;YACb,WAAW,EAAE,2DAA2D;YACxE,QAAQ,EAAE,QAAQ;YAClB,SAAS,EAAE,IAAI;YACf,iBAAiB,EAAE,IAAI;SACxB;QACD;YACE,EAAE,EAAE,eAAe;YACnB,IAAI,EAAE,eAAe;YACrB,WAAW,EAAE,gDAAgD;YAC7D,QAAQ,EAAE,QAAQ;YAClB,SAAS,EAAE,IAAI;YACf,iBAAiB,EAAE,IAAI;SACxB;QACD;YACE,EAAE,EAAE,cAAc;YAClB,IAAI,EAAE,OAAO;YACb,WAAW,EAAE,gDAAgD;YAC7D,QAAQ,EAAE,QAAQ;YAClB,SAAS,EAAE,IAAI;YACf,iBAAiB,EAAE,KAAK;SACzB;KACF,CAAC;IAEM,MAAM,GAAkB,IAAI,CAAC;IAErC,YAAY,MAAe;QACzB,KAAK,CAAC,MAAM,CAAC,CAAC;QACd,IAAI,MAAM,EAAE,CAAC;YACX,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,CAAC;QAChC,CAAC;IACH,CAAC;IAEO,gBAAgB,CAAC,MAAc;QACrC,IAAI,CAAC,MAAM,GAAG,IAAI,gBAAM,CAAC;YACvB,MAAM,EAAE,MAAM;SACf,CAAC,CAAC;IACL,CAAC;IAEM,SAAS,CAAC,MAAc;QAC7B,KAAK,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;QACxB,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,CAAC;IAChC,CAAC;IAED,KAAK,CAAC,cAAc,CAAC,MAAc;QACjC,IAAI,CAAC;YACH,MAAM,UAAU,GAAG,IAAI,gBAAM,CAAC,EAAE,MAAM,EAAE,CAAC,CAAC;YAC1C,MAAM,UAAU,CAAC,MAAM,CAAC,IAAI,EAAE,CAAC;YAC/B,OAAO,IAAI,CAAC;QACd,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,KAAK,CAAC;QACf,CAAC;IACH,CAAC;IAED,KAAK,CAAC,YAAY,CAAC,OAAkB;QACnC,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC;YACjB,MAAM,IAAI,KAAK,CAAC,0DAA0D,CAAC,CAAC;QAC9E,CAAC;QAED,IAAI,CAAC;YACH,MAAM,QAAQ,GAA8B;gBAC1C,aAAa,EAAE,OAAO;gBACtB,eAAe,EAAE,eAAe;gBAChC,cAAc,EAAE,kBAAkB;aACnC,CAAC;YAEF,MAAM,WAAW,GAAG,QAAQ,CAAC,OAAO,CAAC,KAAK,CAAC,IAAI,eAAe,CAAC;YAC/D,MAAM,MAAM,GAAG,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;YAEzC,IAAI,OAAO,CAAC,KAAK,KAAK,cAAc,EAAE,CAAC;gBACrC,+BAA+B;gBAC/B,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,MAAM,CAAC;oBACpD,KAAK,EAAE,WAAW;oBAClB,MAAM,EAAE,MAAM;oBACd,UAAU,EAAE,OAAO,CAAC,SAAS,IAAI,IAAI;oBACrC,WAAW,EAAE,OAAO,CAAC,WAAW,IAAI,GAAG;oBACvC,IAAI,EAAE,CAAC,MAAM,EAAE,KAAK,CAAC;iBACtB,CAAC,CAAC;gBAEH,OAAO;oBACL,OAAO,EAAE,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE;oBAChD,KAAK,EAAE,OAAO,CAAC,KAAK;oBACpB,KAAK,EAAE;wBACL,YAAY,EAAE,QAAQ,CAAC,KAAK,EAAE,aAAa,IAAI,CAAC;wBAChD,gBAAgB,EAAE,QAAQ,CAAC,KAAK,EAAE,iBAAiB,IAAI,CAAC;wBACxD,WAAW,EAAE,QAAQ,CAAC,KAAK,EAAE,YAAY,IAAI,CAAC;qBAC/C;oBACD,YAAY,EAAE,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,aAAa,IAAI,SAAS;iBAC9D,CAAC;YACJ,CAAC;iBAAM,CAAC;gBACN,yCAAyC;gBACzC,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC;oBACzD,KAAK,EAAE,WAAW;oBAClB,QAAQ,EAAE;wBACR;4BACE,IAAI,EAAE,QAAQ;4BACd,OAAO,EAAE,qHAAqH;yBAC/H;wBACD;4BACE,IAAI,EAAE,MAAM;4BACZ,OAAO,EAAE,MAAM;yBAChB;qBACF;oBACD,UAAU,EAAE,OAAO,CAAC,SAAS,IAAI,IAAI;oBACrC,WAAW,EAAE,OAAO,CAAC,WAAW,IAAI,GAAG;iBACxC,CAAC,CAAC;gBAEH,OAAO;oBACL,OAAO,EAAE,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE;oBAC5D,KAAK,EAAE,OAAO,CAAC,KAAK;oBACpB,KAAK,EAAE;wBACL,YAAY,EAAE,QAAQ,CAAC,KAAK,EAAE,aAAa,IAAI,CAAC;wBAChD,gBAAgB,EAAE,QAAQ,CAAC,KAAK,EAAE,iBAAiB,IAAI,CAAC;wBACxD,WAAW,EAAE,QAAQ,CAAC,KAAK,EAAE,YAAY,IAAI,CAAC;qBAC/C;oBACD,YAAY,EAAE,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,aAAa,IAAI,SAAS;iBAC9D,CAAC;YACJ,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;QAC1B,CAAC;IACH,CAAC;CACF;AA/HD,wCA+HC"}