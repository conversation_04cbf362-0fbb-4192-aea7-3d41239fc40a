"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.ContextService = void 0;
const vscode = __importStar(require("vscode"));
class ContextService {
    static extractContext(editor, contextLines = 10) {
        const document = editor.document;
        const selection = editor.selection;
        const position = selection.active;
        const context = {
            fileName: document.fileName,
            language: document.languageId,
            cursorPosition: position,
            lineNumber: position.line + 1
        };
        // Get selected text if any
        if (!selection.isEmpty) {
            context.selectedText = document.getText(selection);
        }
        // Get surrounding code context
        const startLine = Math.max(0, position.line - contextLines);
        const endLine = Math.min(document.lineCount - 1, position.line + contextLines);
        const contextRange = new vscode.Range(startLine, 0, endLine, document.lineAt(endLine).text.length);
        context.surroundingCode = document.getText(contextRange);
        // Try to extract function context
        context.functionContext = this.extractFunctionContext(document, position);
        // Try to extract class context
        context.classContext = this.extractClassContext(document, position);
        return context;
    }
    static extractFunctionContext(document, position) {
        const language = document.languageId;
        let functionPattern;
        // Define function patterns for different languages
        switch (language) {
            case 'javascript':
            case 'typescript':
                functionPattern = /^\s*(function\s+\w+|const\s+\w+\s*=|let\s+\w+\s*=|var\s+\w+\s*=|\w+\s*:\s*function|\w+\s*\(.*\)\s*=>|\w+\s*\(.*\)\s*{)/;
                break;
            case 'python':
                functionPattern = /^\s*def\s+\w+\s*\(/;
                break;
            case 'java':
            case 'csharp':
                functionPattern = /^\s*(public|private|protected|static).*\s+\w+\s*\(/;
                break;
            case 'cpp':
            case 'c':
                functionPattern = /^\s*\w+\s+\w+\s*\(/;
                break;
            default:
                return undefined;
        }
        // Search backwards from current position to find function declaration
        for (let i = position.line; i >= 0; i--) {
            const line = document.lineAt(i);
            if (functionPattern.test(line.text)) {
                return line.text.trim();
            }
        }
        return undefined;
    }
    static extractClassContext(document, position) {
        const language = document.languageId;
        let classPattern;
        // Define class patterns for different languages
        switch (language) {
            case 'javascript':
            case 'typescript':
                classPattern = /^\s*class\s+\w+/;
                break;
            case 'python':
                classPattern = /^\s*class\s+\w+/;
                break;
            case 'java':
            case 'csharp':
                classPattern = /^\s*(public|private|protected)?\s*class\s+\w+/;
                break;
            case 'cpp':
                classPattern = /^\s*class\s+\w+/;
                break;
            default:
                return undefined;
        }
        // Search backwards from current position to find class declaration
        for (let i = position.line; i >= 0; i--) {
            const line = document.lineAt(i);
            if (classPattern.test(line.text)) {
                return line.text.trim();
            }
        }
        return undefined;
    }
    static buildContextPrompt(context) {
        let prompt = '';
        if (context.fileName) {
            prompt += `File: ${context.fileName}\n`;
        }
        if (context.language) {
            prompt += `Language: ${context.language}\n`;
        }
        if (context.lineNumber) {
            prompt += `Line: ${context.lineNumber}\n`;
        }
        if (context.classContext) {
            prompt += `Class: ${context.classContext}\n`;
        }
        if (context.functionContext) {
            prompt += `Function: ${context.functionContext}\n`;
        }
        if (context.selectedText) {
            prompt += `\nSelected code:\n${context.selectedText}\n`;
        }
        if (context.surroundingCode) {
            prompt += `\nSurrounding code:\n${context.surroundingCode}\n`;
        }
        return prompt;
    }
    static getLanguageFromFileName(fileName) {
        const extension = fileName.split('.').pop()?.toLowerCase();
        const languageMap = {
            'js': 'javascript',
            'jsx': 'javascript',
            'ts': 'typescript',
            'tsx': 'typescript',
            'py': 'python',
            'java': 'java',
            'cs': 'csharp',
            'cpp': 'cpp',
            'cc': 'cpp',
            'cxx': 'cpp',
            'c': 'c',
            'h': 'c',
            'hpp': 'cpp',
            'php': 'php',
            'rb': 'ruby',
            'go': 'go',
            'rs': 'rust',
            'swift': 'swift',
            'kt': 'kotlin',
            'scala': 'scala',
            'sh': 'bash',
            'ps1': 'powershell',
            'sql': 'sql',
            'html': 'html',
            'css': 'css',
            'scss': 'scss',
            'sass': 'sass',
            'less': 'less',
            'json': 'json',
            'xml': 'xml',
            'yaml': 'yaml',
            'yml': 'yaml',
            'md': 'markdown',
            'dockerfile': 'dockerfile'
        };
        return languageMap[extension || ''] || 'text';
    }
    static formatCodeForInsertion(code, editor) {
        const document = editor.document;
        const position = editor.selection.active;
        const line = document.lineAt(position.line);
        // Get the indentation of the current line
        const indentMatch = line.text.match(/^(\s*)/);
        const currentIndent = indentMatch ? indentMatch[1] : '';
        // Split the code into lines and apply indentation
        const lines = code.split('\n');
        const indentedLines = lines.map((codeLine, index) => {
            if (index === 0 && position.character > 0) {
                // First line: don't add extra indentation if cursor is not at line start
                return codeLine;
            }
            // Other lines: add current indentation
            return codeLine.trim() ? currentIndent + codeLine : codeLine;
        });
        return indentedLines.join('\n');
    }
    static async insertCodeAtPosition(editor, code, position) {
        const insertPosition = position || editor.selection.active;
        const formattedCode = this.formatCodeForInsertion(code, editor);
        await editor.edit(editBuilder => {
            editBuilder.insert(insertPosition, formattedCode);
        });
    }
    static async replaceSelectedText(editor, code) {
        const selection = editor.selection;
        const formattedCode = this.formatCodeForInsertion(code, editor);
        await editor.edit(editBuilder => {
            if (selection.isEmpty) {
                editBuilder.insert(selection.active, formattedCode);
            }
            else {
                editBuilder.replace(selection, formattedCode);
            }
        });
    }
}
exports.ContextService = ContextService;
//# sourceMappingURL=contextService.js.map