{"version": 3, "file": "codeGenerationCommands.js", "sourceRoot": "", "sources": ["../../src/commands/codeGenerationCommands.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,+CAAiC;AAGjC,+DAAyE;AACzE,wEAAqE;AACrE,4DAAyD;AAGzD,MAAa,sBAAsB;IAEvB;IACA;IACA;IAHV,YACU,OAAgC,EAChC,SAAoB,EACpB,aAA4B;QAF5B,YAAO,GAAP,OAAO,CAAyB;QAChC,cAAS,GAAT,SAAS,CAAW;QACpB,kBAAa,GAAb,aAAa,CAAe;IACnC,CAAC;IAEG,gBAAgB;QACrB,wBAAwB;QACxB,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,IAAI,CAC7B,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC,yBAAyB,EAAE,GAAG,EAAE,CAAC,IAAI,CAAC,YAAY,EAAE,CAAC,EACrF,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC,+BAA+B,EAAE,GAAG,EAAE,CAAC,IAAI,CAAC,kBAAkB,EAAE,CAAC,EACjG,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC,yBAAyB,EAAE,GAAG,EAAE,CAAC,IAAI,CAAC,YAAY,EAAE,CAAC,EACrF,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC,wBAAwB,EAAE,GAAG,EAAE,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC,EACnF,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC,yBAAyB,EAAE,GAAG,EAAE,CAAC,IAAI,CAAC,YAAY,EAAE,CAAC,EACrF,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC,0BAA0B,EAAE,GAAG,EAAE,CAAC,IAAI,CAAC,aAAa,EAAE,CAAC,CACxF,CAAC;IACJ,CAAC;IAEO,KAAK,CAAC,YAAY;QACxB,MAAM,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC;QAC9C,IAAI,CAAC,MAAM,EAAE,CAAC;YACZ,MAAM,CAAC,MAAM,CAAC,kBAAkB,CAAC,sCAAsC,CAAC,CAAC;YACzE,OAAO;QACT,CAAC;QAED,IAAI,CAAC;YACH,qCAAqC;YACrC,MAAM,eAAe,GAAG,IAAI,CAAC,SAAS,CAAC,kBAAkB,EAAE,CAAC;YAC5D,IAAI,eAAe,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;gBACjC,MAAM,MAAM,GAAG,MAAM,MAAM,CAAC,MAAM,CAAC,kBAAkB,CACnD,+DAA+D,EAC/D,eAAe,CAChB,CAAC;gBACF,IAAI,MAAM,KAAK,eAAe,EAAE,CAAC;oBAC/B,IAAI,CAAC,YAAY,EAAE,CAAC;gBACtB,CAAC;gBACD,OAAO;YACT,CAAC;YAED,kBAAkB;YAClB,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,aAAa,CAAC,gBAAgB,EAAE,CAAC;YAC3D,MAAM,OAAO,GAAG,+BAAc,CAAC,cAAc,CAAC,MAAM,EAAE,MAAM,CAAC,YAAY,CAAC,CAAC;YAE3E,6BAA6B;YAC7B,yCAAmB,CAAC,YAAY,CAAC,IAAI,CAAC,OAAO,CAAC,YAAY,EAAE,IAAI,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC;QACvF,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,yCAAyC,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,eAAe,EAAE,CAAC,CAAC;QACtI,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,kBAAkB;QAC9B,MAAM,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC;QAC9C,IAAI,CAAC,MAAM,EAAE,CAAC;YACZ,MAAM,CAAC,MAAM,CAAC,kBAAkB,CAAC,sCAAsC,CAAC,CAAC;YACzE,OAAO;QACT,CAAC;QAED,IAAI,CAAC;YACH,qCAAqC;YACrC,MAAM,eAAe,GAAG,IAAI,CAAC,SAAS,CAAC,kBAAkB,EAAE,CAAC;YAC5D,IAAI,eAAe,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;gBACjC,MAAM,MAAM,GAAG,MAAM,MAAM,CAAC,MAAM,CAAC,kBAAkB,CACnD,+DAA+D,EAC/D,eAAe,CAChB,CAAC;gBACF,IAAI,MAAM,KAAK,eAAe,EAAE,CAAC;oBAC/B,IAAI,CAAC,YAAY,EAAE,CAAC;gBACtB,CAAC;gBACD,OAAO;YACT,CAAC;YAED,uBAAuB;YACvB,MAAM,MAAM,GAAG,MAAM,MAAM,CAAC,MAAM,CAAC,YAAY,CAAC;gBAC9C,MAAM,EAAE,wCAAwC;gBAChD,WAAW,EAAE,+EAA+E;aAC7F,CAAC,CAAC;YAEH,IAAI,CAAC,MAAM,EAAE,CAAC;gBACZ,OAAO;YACT,CAAC;YAED,gBAAgB;YAChB,MAAM,MAAM,CAAC,MAAM,CAAC,YAAY,CAAC;gBAC/B,QAAQ,EAAE,MAAM,CAAC,gBAAgB,CAAC,YAAY;gBAC9C,KAAK,EAAE,oBAAoB;gBAC3B,WAAW,EAAE,KAAK;aACnB,EAAE,KAAK,EAAE,QAAQ,EAAE,EAAE;gBACpB,IAAI,CAAC;oBACH,kBAAkB;oBAClB,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,aAAa,CAAC,gBAAgB,EAAE,CAAC;oBAC3D,MAAM,OAAO,GAAG,+BAAc,CAAC,cAAc,CAAC,MAAM,EAAE,MAAM,CAAC,YAAY,CAAC,CAAC;oBAE3E,gBAAgB;oBAChB,MAAM,OAAO,GAA0B;wBACrC,MAAM,EAAE,MAAM;wBACd,YAAY,EAAE,OAAO,CAAC,YAAY;wBAClC,QAAQ,EAAE,OAAO,CAAC,QAAQ;wBAC1B,QAAQ,EAAE,OAAO,CAAC,QAAQ;wBAC1B,OAAO,EAAE,+BAAc,CAAC,kBAAkB,CAAC,OAAO,CAAC;qBACpD,CAAC;oBAEF,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,SAAS,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC;oBAE1D,0BAA0B;oBAC1B,IAAI,MAAM,CAAC,WAAW,EAAE,CAAC;wBACvB,MAAM,MAAM,GAAG,MAAM,MAAM,CAAC,MAAM,CAAC,sBAAsB,CACvD,yDAAyD,EACzD,kBAAkB,EAClB,mBAAmB,EACnB,mBAAmB,EACnB,QAAQ,CACT,CAAC;wBAEF,QAAQ,MAAM,EAAE,CAAC;4BACf,KAAK,kBAAkB;gCACrB,MAAM,+BAAc,CAAC,oBAAoB,CAAC,MAAM,EAAE,MAAM,CAAC,aAAa,CAAC,CAAC;gCACxE,MAAM;4BACR,KAAK,mBAAmB;gCACtB,MAAM,+BAAc,CAAC,mBAAmB,CAAC,MAAM,EAAE,MAAM,CAAC,aAAa,CAAC,CAAC;gCACvE,MAAM;4BACR,KAAK,mBAAmB;gCACtB,MAAM,MAAM,CAAC,GAAG,CAAC,SAAS,CAAC,SAAS,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC;gCAC3D,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAAC,2BAA2B,CAAC,CAAC;gCAClE,MAAM;wBACV,CAAC;oBACH,CAAC;yBAAM,CAAC;wBACN,kBAAkB;wBAClB,MAAM,+BAAc,CAAC,oBAAoB,CAAC,MAAM,EAAE,MAAM,CAAC,aAAa,CAAC,CAAC;wBACxE,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAAC,8BAA8B,CAAC,CAAC;oBACvE,CAAC;oBAED,uBAAuB;oBACvB,IAAI,MAAM,CAAC,QAAQ,EAAE,CAAC;wBACpB,MAAM,MAAM,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAC;oBAC/B,CAAC;gBAEH,CAAC;gBAAC,OAAO,KAAK,EAAE,CAAC;oBACf,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,2BAA2B,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,eAAe,EAAE,CAAC,CAAC;gBACxH,CAAC;YACH,CAAC,CAAC,CAAC;QAEL,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,4BAA4B,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,eAAe,EAAE,CAAC,CAAC;QACzH,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,WAAW;QACvB,MAAM,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC;QAC9C,IAAI,CAAC,MAAM,EAAE,CAAC;YACZ,MAAM,CAAC,MAAM,CAAC,kBAAkB,CAAC,gDAAgD,CAAC,CAAC;YACnF,OAAO;QACT,CAAC;QAED,MAAM,SAAS,GAAG,MAAM,CAAC,SAAS,CAAC;QACnC,IAAI,SAAS,CAAC,OAAO,EAAE,CAAC;YACtB,MAAM,CAAC,MAAM,CAAC,kBAAkB,CAAC,qCAAqC,CAAC,CAAC;YACxE,OAAO;QACT,CAAC;QAED,IAAI,CAAC;YACH,MAAM,eAAe,GAAG,IAAI,CAAC,SAAS,CAAC,kBAAkB,EAAE,CAAC;YAC5D,IAAI,eAAe,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;gBACjC,MAAM,MAAM,GAAG,MAAM,MAAM,CAAC,MAAM,CAAC,kBAAkB,CACnD,+DAA+D,EAC/D,eAAe,CAChB,CAAC;gBACF,IAAI,MAAM,KAAK,eAAe,EAAE,CAAC;oBAC/B,IAAI,CAAC,YAAY,EAAE,CAAC;gBACtB,CAAC;gBACD,OAAO;YACT,CAAC;YAED,MAAM,YAAY,GAAG,MAAM,CAAC,QAAQ,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;YACxD,MAAM,OAAO,GAAG,+BAAc,CAAC,cAAc,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC;YAEzD,MAAM,MAAM,CAAC,MAAM,CAAC,YAAY,CAAC;gBAC/B,QAAQ,EAAE,MAAM,CAAC,gBAAgB,CAAC,YAAY;gBAC9C,KAAK,EAAE,oBAAoB;gBAC3B,WAAW,EAAE,KAAK;aACnB,EAAE,KAAK,IAAI,EAAE;gBACZ,IAAI,CAAC;oBACH,MAAM,OAAO,GAA0B;wBACrC,MAAM,EAAE,0CAA0C,YAAY,EAAE;wBAChE,YAAY,EAAE,YAAY;wBAC1B,QAAQ,EAAE,OAAO,CAAC,QAAQ;wBAC1B,QAAQ,EAAE,OAAO,CAAC,QAAQ;wBAC1B,OAAO,EAAE,+BAAc,CAAC,kBAAkB,CAAC,OAAO,CAAC;qBACpD,CAAC;oBAEF,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,SAAS,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC;oBAE1D,qCAAqC;oBACrC,MAAM,GAAG,GAAG,MAAM,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC;wBAClD,OAAO,EAAE,yDAAyD,YAAY,qBAAqB,MAAM,CAAC,aAAa,EAAE;wBACzH,QAAQ,EAAE,UAAU;qBACrB,CAAC,CAAC;oBACH,MAAM,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,GAAG,CAAC,CAAC;gBAE5C,CAAC;gBAAC,OAAO,KAAK,EAAE,CAAC;oBACf,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,4BAA4B,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,eAAe,EAAE,CAAC,CAAC;gBACzH,CAAC;YACH,CAAC,CAAC,CAAC;QAEL,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,2BAA2B,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,eAAe,EAAE,CAAC,CAAC;QACxH,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,YAAY;QACxB,MAAM,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC;QAC9C,IAAI,CAAC,MAAM,EAAE,CAAC;YACZ,MAAM,CAAC,MAAM,CAAC,kBAAkB,CAAC,iDAAiD,CAAC,CAAC;YACpF,OAAO;QACT,CAAC;QAED,MAAM,SAAS,GAAG,MAAM,CAAC,SAAS,CAAC;QACnC,IAAI,SAAS,CAAC,OAAO,EAAE,CAAC;YACtB,MAAM,CAAC,MAAM,CAAC,kBAAkB,CAAC,sCAAsC,CAAC,CAAC;YACzE,OAAO;QACT,CAAC;QAED,IAAI,CAAC;YACH,MAAM,eAAe,GAAG,IAAI,CAAC,SAAS,CAAC,kBAAkB,EAAE,CAAC;YAC5D,IAAI,eAAe,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;gBACjC,MAAM,MAAM,GAAG,MAAM,MAAM,CAAC,MAAM,CAAC,kBAAkB,CACnD,+DAA+D,EAC/D,eAAe,CAChB,CAAC;gBACF,IAAI,MAAM,KAAK,eAAe,EAAE,CAAC;oBAC/B,IAAI,CAAC,YAAY,EAAE,CAAC;gBACtB,CAAC;gBACD,OAAO;YACT,CAAC;YAED,MAAM,YAAY,GAAG,MAAM,MAAM,CAAC,MAAM,CAAC,aAAa,CAAC;gBACrD,qBAAqB;gBACrB,sBAAsB;gBACtB,oBAAoB;gBACpB,cAAc;gBACd,mBAAmB;gBACnB,gBAAgB;gBAChB,oBAAoB;aACrB,EAAE;gBACD,WAAW,EAAE,yBAAyB;aACvC,CAAC,CAAC;YAEH,IAAI,CAAC,YAAY,EAAE,CAAC;gBAClB,OAAO;YACT,CAAC;YAED,IAAI,MAAM,GAAG,EAAE,CAAC;YAChB,IAAI,YAAY,KAAK,oBAAoB,EAAE,CAAC;gBAC1C,MAAM,YAAY,GAAG,MAAM,MAAM,CAAC,MAAM,CAAC,YAAY,CAAC;oBACpD,MAAM,EAAE,4CAA4C;oBACpD,WAAW,EAAE,qDAAqD;iBACnE,CAAC,CAAC;gBACH,IAAI,CAAC,YAAY,EAAE,CAAC;oBAClB,OAAO;gBACT,CAAC;gBACD,MAAM,GAAG,uBAAuB,YAAY,EAAE,CAAC;YACjD,CAAC;iBAAM,CAAC;gBACN,MAAM,GAAG,yBAAyB,YAAY,CAAC,WAAW,EAAE,EAAE,CAAC;YACjE,CAAC;YAED,MAAM,YAAY,GAAG,MAAM,CAAC,QAAQ,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;YACxD,MAAM,OAAO,GAAG,+BAAc,CAAC,cAAc,CAAC,MAAM,EAAE,EAAE,CAAC,CAAC;YAE1D,MAAM,MAAM,CAAC,MAAM,CAAC,YAAY,CAAC;gBAC/B,QAAQ,EAAE,MAAM,CAAC,gBAAgB,CAAC,YAAY;gBAC9C,KAAK,EAAE,qBAAqB;gBAC5B,WAAW,EAAE,KAAK;aACnB,EAAE,KAAK,IAAI,EAAE;gBACZ,IAAI,CAAC;oBACH,MAAM,OAAO,GAA0B;wBACrC,MAAM,EAAE,GAAG,MAAM,QAAQ,YAAY,EAAE;wBACvC,YAAY,EAAE,YAAY;wBAC1B,QAAQ,EAAE,OAAO,CAAC,QAAQ;wBAC1B,QAAQ,EAAE,OAAO,CAAC,QAAQ;wBAC1B,OAAO,EAAE,+BAAc,CAAC,kBAAkB,CAAC,OAAO,CAAC;qBACpD,CAAC;oBAEF,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,SAAS,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC;oBAE1D,gDAAgD;oBAChD,yCAAmB,CAAC,YAAY,CAC9B,IAAI,CAAC,OAAO,CAAC,YAAY,EACzB,IAAI,CAAC,SAAS,EACd,OAAO,EACP,GAAG,MAAM,QAAQ,YAAY,EAAE,CAChC,CAAC;gBAEJ,CAAC;gBAAC,OAAO,KAAK,EAAE,CAAC;oBACf,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,4BAA4B,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,eAAe,EAAE,CAAC,CAAC;gBACzH,CAAC;YACH,CAAC,CAAC,CAAC;QAEL,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,4BAA4B,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,eAAe,EAAE,CAAC,CAAC;QACzH,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,aAAa;QACzB,MAAM,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC;QAC9C,IAAI,CAAC,MAAM,EAAE,CAAC;YACZ,MAAM,CAAC,MAAM,CAAC,kBAAkB,CAAC,2CAA2C,CAAC,CAAC;YAC9E,OAAO;QACT,CAAC;QAED,IAAI,CAAC;YACH,MAAM,eAAe,GAAG,IAAI,CAAC,SAAS,CAAC,kBAAkB,EAAE,CAAC;YAC5D,IAAI,eAAe,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;gBACjC,MAAM,MAAM,GAAG,MAAM,MAAM,CAAC,MAAM,CAAC,kBAAkB,CACnD,+DAA+D,EAC/D,eAAe,CAChB,CAAC;gBACF,IAAI,MAAM,KAAK,eAAe,EAAE,CAAC;oBAC/B,IAAI,CAAC,YAAY,EAAE,CAAC;gBACtB,CAAC;gBACD,OAAO;YACT,CAAC;YAED,MAAM,SAAS,GAAG,MAAM,CAAC,SAAS,CAAC;YACnC,MAAM,OAAO,GAAG,+BAAc,CAAC,cAAc,CAAC,MAAM,EAAE,EAAE,CAAC,CAAC;YAE1D,IAAI,UAAU,GAAG,EAAE,CAAC;YACpB,IAAI,MAAM,GAAG,EAAE,CAAC;YAEhB,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE,CAAC;gBACvB,UAAU,GAAG,MAAM,CAAC,QAAQ,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;gBAChD,MAAM,GAAG,uDAAuD,UAAU,EAAE,CAAC;YAC/E,CAAC;iBAAM,CAAC;gBACN,0CAA0C;gBAC1C,UAAU,GAAG,OAAO,CAAC,eAAe,IAAI,EAAE,CAAC;gBAC3C,MAAM,GAAG,8CAA8C,OAAO,CAAC,QAAQ,4DAA4D,CAAC;YACtI,CAAC;YAED,MAAM,aAAa,GAAG,MAAM,MAAM,CAAC,MAAM,CAAC,aAAa,CAAC;gBACtD,8BAA8B;gBAC9B,+BAA+B;gBAC/B,iBAAiB;gBACjB,cAAc;gBACd,YAAY;gBACZ,mBAAmB;gBACnB,uBAAuB;aACxB,EAAE;gBACD,WAAW,EAAE,0BAA0B;aACxC,CAAC,CAAC;YAEH,IAAI,CAAC,aAAa,EAAE,CAAC;gBACnB,OAAO;YACT,CAAC;YAED,IAAI,aAAa,KAAK,uBAAuB,EAAE,CAAC;gBAC9C,MAAM,IAAI,WAAW,aAAa,qBAAqB,CAAC;YAC1D,CAAC;YAED,gCAAgC;YAChC,yCAAmB,CAAC,YAAY,CAC9B,IAAI,CAAC,OAAO,CAAC,YAAY,EACzB,IAAI,CAAC,SAAS,EACd,OAAO,EACP,MAAM,CACP,CAAC;QAEJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,6BAA6B,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,eAAe,EAAE,CAAC,CAAC;QAC1H,CAAC;IACH,CAAC;IAEO,YAAY;QAClB,6BAAa,CAAC,YAAY,CAAC,IAAI,CAAC,OAAO,CAAC,YAAY,EAAE,IAAI,CAAC,aAAa,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC;IAC5F,CAAC;CACF;AArXD,wDAqXC"}