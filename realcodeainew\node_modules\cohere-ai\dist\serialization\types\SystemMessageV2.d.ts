/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as serializers from "../index";
import * as Cohere from "../../api/index";
import * as core from "../../core";
import { SystemMessageV2Content } from "./SystemMessageV2Content";
export declare const SystemMessageV2: core.serialization.ObjectSchema<serializers.SystemMessageV2.Raw, Cohere.SystemMessageV2>;
export declare namespace SystemMessageV2 {
    interface Raw {
        content: SystemMessageV2Content.Raw;
    }
}
