/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as serializers from "../index";
import * as Cohere from "../../api/index";
import * as core from "../../core";
import { FinetuneDatasetMetrics } from "./FinetuneDatasetMetrics";
export declare const Metrics: core.serialization.ObjectSchema<serializers.Metrics.Raw, Cohere.Metrics>;
export declare namespace Metrics {
    interface Raw {
        finetune_dataset_metrics?: FinetuneDatasetMetrics.Raw | null;
    }
}
