/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as Cohere from "../../../../../index";
/**
 * Response to a request to list events of a fine-tuned model.
 */
export interface ListEventsResponse {
    /** List of events for the fine-tuned model. */
    events?: Cohere.finetuning.Event[];
    /**
     * Pagination token to retrieve the next page of results. If the value is "",
     * it means no further results for the request.
     */
    nextPageToken?: string;
    /** Total count of results. */
    totalSize?: number;
}
