{"version": 3, "file": "aiService.js", "sourceRoot": "", "sources": ["../../src/services/aiService.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,+CAAiC;AACjC,oCAAsJ;AACtJ,gDAAqD;AACrD,sDAA2D;AAC3D,gDAAqD;AACrD,gDAAqD;AACrD,0DAA+D;AAC/D,kDAAuD;AAEvD,MAAa,SAAS;IACZ,SAAS,GAA4B,IAAI,GAAG,EAAE,CAAC;IAC/C,MAAM,CAAkB;IAEhC;QACE,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,iBAAiB,EAAE,CAAC;QACvC,IAAI,CAAC,mBAAmB,EAAE,CAAC;IAC7B,CAAC;IAEO,iBAAiB;QACvB,MAAM,MAAM,GAAG,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC,YAAY,CAAC,CAAC;QAC/D,OAAO;YACL,YAAY,EAAE,MAAM,CAAC,GAAG,CAAC,cAAc,EAAE,eAAe,CAAC;YACzD,WAAW,EAAE,MAAM,CAAC,GAAG,CAAC,aAAa,EAAE,GAAG,CAAC;YAC3C,SAAS,EAAE,MAAM,CAAC,GAAG,CAAC,WAAW,EAAE,IAAI,CAAC;YACxC,cAAc,EAAE,MAAM,CAAC,GAAG,CAAC,gBAAgB,EAAE,IAAI,CAAC;YAClD,YAAY,EAAE,MAAM,CAAC,GAAG,CAAC,cAAc,EAAE,EAAE,CAAC;YAC5C,QAAQ,EAAE,MAAM,CAAC,GAAG,CAAC,UAAU,EAAE,KAAK,CAAC;YACvC,WAAW,EAAE,MAAM,CAAC,GAAG,CAAC,aAAa,EAAE,IAAI,CAAC;YAC5C,OAAO,EAAE,IAAI,CAAC,WAAW,EAAE;SAC5B,CAAC;IACJ,CAAC;IAEO,WAAW;QACjB,MAAM,IAAI,GAAmC,EAAE,CAAC;QAChD,MAAM,SAAS,GAAG,CAAC,QAAQ,EAAE,WAAW,EAAE,QAAQ,EAAE,QAAQ,EAAE,aAAa,EAAE,SAAS,CAAC,CAAC;QAExF,KAAK,MAAM,QAAQ,IAAI,SAAS,EAAE,CAAC;YACjC,MAAM,GAAG,GAAG,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC,YAAY,CAAC,CAAC,GAAG,CAAC,WAAW,QAAQ,EAAE,EAAE,EAAE,CAAC,CAAC;YAC3F,IAAI,GAAG,EAAE,CAAC;gBACR,IAAI,CAAC,QAAQ,CAAC,GAAG,GAAG,CAAC;YACvB,CAAC;QACH,CAAC;QAED,OAAO,IAAI,CAAC;IACd,CAAC;IAEO,mBAAmB;QACzB,2BAA2B;QAC3B,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,QAAQ,EAAE,IAAI,uBAAc,CAAC,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC;QAC7E,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,WAAW,EAAE,IAAI,6BAAiB,CAAC,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,CAAC;QACtF,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,QAAQ,EAAE,IAAI,uBAAc,CAAC,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC;QAC7E,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,QAAQ,EAAE,IAAI,uBAAc,CAAC,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC;QAC7E,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,aAAa,EAAE,IAAI,iCAAmB,CAAC,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC,CAAC;QAC5F,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,SAAS,EAAE,IAAI,yBAAe,CAAC,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC;IAClF,CAAC;IAEM,oBAAoB;QACzB,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,iBAAiB,EAAE,CAAC;QACvC,IAAI,CAAC,qBAAqB,EAAE,CAAC;IAC/B,CAAC;IAEO,qBAAqB;QAC3B,KAAK,MAAM,CAAC,YAAY,EAAE,QAAQ,CAAC,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC;YACtD,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC;YACjD,IAAI,MAAM,EAAE,CAAC;gBACX,QAAQ,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;YAC7B,CAAC;QACH,CAAC;IACH,CAAC;IAEM,kBAAkB;QACvB,OAAO,wBAAgB,CAAC;IAC1B,CAAC;IAEM,kBAAkB;QACvB,OAAO,wBAAgB,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE;YACrC,MAAM,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;YACpD,OAAO,QAAQ,IAAI,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;QACzD,CAAC,CAAC,CAAC;IACL,CAAC;IAEO,mBAAmB,CAAC,OAAe;QACzC,MAAM,KAAK,GAAG,wBAAgB,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,KAAK,OAAO,CAAC,CAAC;QAC3D,IAAI,CAAC,KAAK,EAAE,CAAC;YACX,MAAM,IAAI,KAAK,CAAC,sBAAsB,OAAO,EAAE,CAAC,CAAC;QACnD,CAAC;QAED,MAAM,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;QACpD,IAAI,CAAC,QAAQ,EAAE,CAAC;YACd,MAAM,IAAI,KAAK,CAAC,iCAAiC,OAAO,EAAE,CAAC,CAAC;QAC9D,CAAC;QAED,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,QAAQ,CAAC,EAAE,CAAC;YACzC,MAAM,IAAI,KAAK,CAAC,8BAA8B,KAAK,CAAC,QAAQ,oCAAoC,CAAC,CAAC;QACpG,CAAC;QAED,OAAO,QAAQ,CAAC;IAClB,CAAC;IAEM,KAAK,CAAC,cAAc,CAAC,QAAgB,EAAE,MAAc;QAC1D,MAAM,gBAAgB,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;QACtD,IAAI,CAAC,gBAAgB,EAAE,CAAC;YACtB,OAAO,KAAK,CAAC;QACf,CAAC;QAED,IAAI,CAAC;YACH,OAAO,MAAM,gBAAgB,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC;QACvD,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,gCAAgC,QAAQ,GAAG,EAAE,KAAK,CAAC,CAAC;YAClE,OAAO,KAAK,CAAC;QACf,CAAC;IACH,CAAC;IAEM,KAAK,CAAC,YAAY,CAAC,OAA8B;QACtD,MAAM,OAAO,GAAG,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC;QACzC,MAAM,QAAQ,GAAG,IAAI,CAAC,mBAAmB,CAAC,OAAO,CAAC,CAAC;QAEnD,2BAA2B;QAC3B,IAAI,OAAO,GAAG,EAAE,CAAC;QACjB,IAAI,IAAI,CAAC,MAAM,CAAC,cAAc,IAAI,OAAO,CAAC,OAAO,EAAE,CAAC;YAClD,OAAO,GAAG,OAAO,CAAC,OAAO,CAAC;QAC5B,CAAC;QAED,MAAM,SAAS,GAAc;YAC3B,MAAM,EAAE,OAAO,CAAC,MAAM;YACtB,KAAK,EAAE,OAAO;YACd,WAAW,EAAE,IAAI,CAAC,MAAM,CAAC,WAAW;YACpC,SAAS,EAAE,IAAI,CAAC,MAAM,CAAC,SAAS;YAChC,OAAO,EAAE,OAAO;YAChB,QAAQ,EAAE,OAAO,CAAC,QAAQ;SAC3B,CAAC;QAEF,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,MAAM,QAAQ,CAAC,YAAY,CAAC,SAAS,CAAC,CAAC;YAExD,OAAO;gBACL,aAAa,EAAE,QAAQ,CAAC,OAAO;gBAC/B,KAAK,EAAE,OAAO;gBACd,WAAW,EAAE,IAAI,CAAC,kBAAkB,CAAC,QAAQ,CAAC,OAAO,CAAC;gBACtD,WAAW,EAAE,IAAI,CAAC,mBAAmB,CAAC,QAAQ,CAAC,OAAO,CAAC;gBACvD,UAAU,EAAE,IAAI,CAAC,mBAAmB,CAAC,QAAQ,CAAC;aAC/C,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,IAAI,KAAK,CAAC,2BAA2B,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,eAAe,EAAE,CAAC,CAAC;QACzG,CAAC;IACH,CAAC;IAEM,KAAK,CAAC,qBAAqB,CAAC,OAA8B,EAAE,OAAe;QAChF,MAAM,QAAQ,GAAG,IAAI,CAAC,mBAAmB,CAAC,OAAO,CAAC,CAAC;QAEnD,IAAI,OAAO,GAAG,EAAE,CAAC;QACjB,IAAI,IAAI,CAAC,MAAM,CAAC,cAAc,IAAI,OAAO,CAAC,OAAO,EAAE,CAAC;YAClD,OAAO,GAAG,OAAO,CAAC,OAAO,CAAC;QAC5B,CAAC;QAED,MAAM,SAAS,GAAc;YAC3B,MAAM,EAAE,OAAO,CAAC,MAAM;YACtB,KAAK,EAAE,OAAO;YACd,WAAW,EAAE,IAAI,CAAC,MAAM,CAAC,WAAW;YACpC,SAAS,EAAE,IAAI,CAAC,MAAM,CAAC,SAAS;YAChC,OAAO,EAAE,OAAO;YAChB,QAAQ,EAAE,OAAO,CAAC,QAAQ;SAC3B,CAAC;QAEF,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,MAAM,QAAQ,CAAC,YAAY,CAAC,SAAS,CAAC,CAAC;YAExD,OAAO;gBACL,aAAa,EAAE,QAAQ,CAAC,OAAO;gBAC/B,KAAK,EAAE,OAAO;gBACd,WAAW,EAAE,IAAI,CAAC,kBAAkB,CAAC,QAAQ,CAAC,OAAO,CAAC;gBACtD,WAAW,EAAE,IAAI,CAAC,mBAAmB,CAAC,QAAQ,CAAC,OAAO,CAAC;gBACvD,UAAU,EAAE,IAAI,CAAC,mBAAmB,CAAC,QAAQ,CAAC;aAC/C,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,IAAI,KAAK,CAAC,2BAA2B,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,eAAe,EAAE,CAAC,CAAC;QACzG,CAAC;IACH,CAAC;IAEO,kBAAkB,CAAC,OAAe;QACxC,yDAAyD;QACzD,MAAM,mBAAmB,GAAG;YAC1B,kBAAkB;YAClB,aAAa;YACb,cAAc;YACd,cAAc;SACf,CAAC;QAEF,KAAK,MAAM,OAAO,IAAI,mBAAmB,EAAE,CAAC;YAC1C,MAAM,KAAK,GAAG,OAAO,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;YACrC,IAAI,KAAK,IAAI,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC;gBACtB,OAAO,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC;YACzB,CAAC;QACH,CAAC;QAED,OAAO,SAAS,CAAC;IACnB,CAAC;IAEO,mBAAmB,CAAC,OAAe;QACzC,MAAM,WAAW,GAAa,EAAE,CAAC;QAEjC,8CAA8C;QAC9C,IAAI,OAAO,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,OAAO,CAAC,QAAQ,CAAC,OAAO,CAAC,EAAE,CAAC;YAC1D,WAAW,CAAC,IAAI,CAAC,4CAA4C,CAAC,CAAC;QACjE,CAAC;QAED,IAAI,OAAO,CAAC,QAAQ,CAAC,aAAa,CAAC,IAAI,OAAO,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAAE,CAAC;YAClE,WAAW,CAAC,IAAI,CAAC,2CAA2C,CAAC,CAAC;QAChE,CAAC;QAED,IAAI,OAAO,CAAC,MAAM,GAAG,EAAE,EAAE,CAAC;YACxB,WAAW,CAAC,IAAI,CAAC,8CAA8C,CAAC,CAAC;QACnE,CAAC;QAED,OAAO,WAAW,CAAC;IACrB,CAAC;IAEO,mBAAmB,CAAC,QAAoB;QAC9C,kEAAkE;QAClE,IAAI,UAAU,GAAG,GAAG,CAAC,CAAC,kBAAkB;QAExC,IAAI,QAAQ,CAAC,YAAY,KAAK,MAAM,EAAE,CAAC;YACrC,UAAU,IAAI,GAAG,CAAC;QACpB,CAAC;QAED,IAAI,QAAQ,CAAC,KAAK,IAAI,QAAQ,CAAC,KAAK,CAAC,WAAW,GAAG,CAAC,EAAE,CAAC;YACrD,2DAA2D;YAC3D,MAAM,UAAU,GAAG,QAAQ,CAAC,KAAK,CAAC,gBAAgB,GAAG,QAAQ,CAAC,KAAK,CAAC,WAAW,CAAC;YAChF,UAAU,IAAI,UAAU,GAAG,GAAG,CAAC;QACjC,CAAC;QAED,OAAO,IAAI,CAAC,GAAG,CAAC,UAAU,EAAE,GAAG,CAAC,CAAC;IACnC,CAAC;CACF;AAhOD,8BAgOC"}