/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as serializers from "../index";
import * as Cohere from "../../api/index";
import * as core from "../../core";
export declare const UsageBilledUnits: core.serialization.ObjectSchema<serializers.UsageBilledUnits.Raw, Cohere.UsageBilledUnits>;
export declare namespace UsageBilledUnits {
    interface Raw {
        input_tokens?: number | null;
        output_tokens?: number | null;
        search_units?: number | null;
        classifications?: number | null;
    }
}
