/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as serializers from "../index";
import * as Cohere from "../../api/index";
import * as core from "../../core";
import { ToolCallV2Function } from "./ToolCallV2Function";
export declare const ToolCallV2: core.serialization.ObjectSchema<serializers.ToolCallV2.Raw, Cohere.ToolCallV2>;
export declare namespace ToolCallV2 {
    interface Raw {
        id?: string | null;
        type?: "function" | null;
        function?: ToolCallV2Function.Raw | null;
    }
}
