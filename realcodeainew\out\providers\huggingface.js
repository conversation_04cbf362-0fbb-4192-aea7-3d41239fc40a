"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.HuggingFaceProvider = void 0;
const axios_1 = __importDefault(require("axios"));
const base_1 = require("./base");
class HuggingFaceProvider extends base_1.BaseAIProvider {
    name = 'Hugging Face';
    models = [
        {
            id: 'huggingface-codegen',
            name: 'CodeGen',
            description: 'Open source code generation model',
            provider: 'huggingface',
            maxTokens: 2048,
            supportsStreaming: false
        }
    ];
    baseUrl = 'https://api-inference.huggingface.co/models';
    setApiKey(apiKey) {
        super.setApiKey(apiKey);
    }
    async validateApiKey(apiKey) {
        try {
            const response = await axios_1.default.post(`${this.baseUrl}/Salesforce/codegen-350M-mono`, { inputs: 'def hello():' }, {
                headers: {
                    'Authorization': `Bearer ${apiKey}`,
                    'Content-Type': 'application/json'
                },
                timeout: 10000
            });
            return response.status === 200;
        }
        catch (error) {
            return false;
        }
    }
    async generateCode(request) {
        if (!this.apiKey) {
            throw new Error('Hugging Face API key not set. Please set API key first.');
        }
        try {
            const prompt = this.buildPrompt(request);
            // Use CodeGen model for code generation
            const response = await axios_1.default.post(`${this.baseUrl}/Salesforce/codegen-350M-mono`, {
                inputs: prompt,
                parameters: {
                    max_new_tokens: request.maxTokens || 1000,
                    temperature: request.temperature || 0.3,
                    do_sample: true,
                    return_full_text: false
                }
            }, {
                headers: {
                    'Authorization': `Bearer ${this.apiKey}`,
                    'Content-Type': 'application/json'
                },
                timeout: 30000
            });
            const generatedText = response.data[0]?.generated_text || '';
            return {
                content: generatedText.trim(),
                model: request.model,
                usage: {
                    promptTokens: 0, // HuggingFace doesn't provide token usage
                    completionTokens: 0,
                    totalTokens: 0
                },
                finishReason: 'stop'
            };
        }
        catch (error) {
            if (axios_1.default.isAxiosError(error)) {
                if (error.response?.status === 503) {
                    throw new Error('Model is currently loading. Please try again in a few minutes.');
                }
            }
            this.handleError(error);
        }
    }
}
exports.HuggingFaceProvider = HuggingFaceProvider;
//# sourceMappingURL=huggingface.js.map