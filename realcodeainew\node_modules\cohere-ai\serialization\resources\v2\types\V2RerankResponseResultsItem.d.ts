/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as serializers from "../../../index";
import * as Cohere from "../../../../api/index";
import * as core from "../../../../core";
export declare const V2RerankResponseResultsItem: core.serialization.ObjectSchema<serializers.V2RerankResponseResultsItem.Raw, Cohere.V2RerankResponseResultsItem>;
export declare namespace V2RerankResponseResultsItem {
    interface Raw {
        index: number;
        relevance_score: number;
    }
}
