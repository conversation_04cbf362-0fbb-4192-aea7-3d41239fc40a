/**
 * This file was auto-generated by <PERSON>rn from our API Definition.
 */
import * as serializers from "../../../../index";
import * as Cohere from "../../../../../api/index";
import * as core from "../../../../../core";
import { EmbedInputType } from "../../../../types/EmbedInputType";
import { EmbedInput } from "../../../../types/EmbedInput";
import { EmbeddingType } from "../../../../types/EmbeddingType";
import { V2EmbedRequestTruncate } from "../../types/V2EmbedRequestTruncate";
export declare const V2EmbedRequest: core.serialization.Schema<serializers.V2EmbedRequest.Raw, Cohere.V2EmbedRequest>;
export declare namespace V2EmbedRequest {
    interface Raw {
        texts?: string[] | null;
        images?: string[] | null;
        model: string;
        input_type: EmbedInputType.Raw;
        inputs?: EmbedInput.Raw[] | null;
        max_tokens?: number | null;
        output_dimension?: number | null;
        embedding_types?: EmbeddingType.Raw[] | null;
        truncate?: V2EmbedRequestTruncate.Raw | null;
    }
}
