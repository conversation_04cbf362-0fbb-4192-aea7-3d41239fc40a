/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as Cohere from "../index";
/**
 * A message with Tool outputs.
 */
export interface ToolMessageV2 {
    /** The id of the associated tool call that has provided the given content */
    toolCallId: string;
    /** Outputs from a tool. The content should formatted as a JSON object string, or a list of tool content blocks */
    content: Cohere.ToolMessageV2Content;
}
