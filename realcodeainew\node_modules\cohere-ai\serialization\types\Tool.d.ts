/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as serializers from "../index";
import * as Cohere from "../../api/index";
import * as core from "../../core";
import { ToolParameterDefinitionsValue } from "./ToolParameterDefinitionsValue";
export declare const Tool: core.serialization.ObjectSchema<serializers.Tool.Raw, Cohere.Tool>;
export declare namespace Tool {
    interface Raw {
        name: string;
        description: string;
        parameter_definitions?: Record<string, ToolParameterDefinitionsValue.Raw> | null;
    }
}
