/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as serializers from "../index";
import * as Cohere from "../../api/index";
import * as core from "../../core";
import { ChatCitation } from "./ChatCitation";
import { ChatDocument } from "./ChatDocument";
import { ChatSearchQuery } from "./ChatSearchQuery";
import { ChatSearchResult } from "./ChatSearchResult";
import { FinishReason } from "./FinishReason";
import { ToolCall } from "./ToolCall";
import { Message } from "./Message";
import { ApiMeta } from "./ApiMeta";
export declare const NonStreamedChatResponse: core.serialization.ObjectSchema<serializers.NonStreamedChatResponse.Raw, Cohere.NonStreamedChatResponse>;
export declare namespace NonStreamedChatResponse {
    interface Raw {
        text: string;
        generation_id?: string | null;
        response_id?: string | null;
        citations?: ChatCitation.Raw[] | null;
        documents?: ChatDocument.Raw[] | null;
        is_search_required?: boolean | null;
        search_queries?: ChatSearchQuery.Raw[] | null;
        search_results?: ChatSearchResult.Raw[] | null;
        finish_reason?: FinishReason.Raw | null;
        tool_calls?: ToolCall.Raw[] | null;
        chat_history?: Message.Raw[] | null;
        meta?: ApiMeta.Raw | null;
    }
}
