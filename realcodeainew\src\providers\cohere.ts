import { CohereClient } from 'cohere-ai';
import { BaseAIProvider } from './base';
import { AIRequest, AIResponse, AIModel } from '../types';

export class CohereProvider extends BaseAIProvider {
  name = 'Cohere';
  models: AIModel[] = [
    {
      id: 'cohere-command',
      name: 'Command',
      description: 'Cohere\'s model for text and code generation',
      provider: 'cohere',
      maxTokens: 2048,
      supportsStreaming: true
    }
  ];

  private client: CohereClient | null = null;

  constructor(apiKey?: string) {
    super(apiKey);
    if (apiKey) {
      this.initializeClient(apiKey);
    }
  }

  private initializeClient(apiKey: string): void {
    this.client = new CohereClient({
      token: apiKey,
    });
  }

  public setApiKey(apiKey: string): void {
    super.setApiKey(apiKey);
    this.initializeClient(apiKey);
  }

  async validateApiKey(apiKey: string): Promise<boolean> {
    try {
      const tempClient = new CohereClient({ token: api<PERSON><PERSON> });
      await tempClient.generate({
        model: 'command',
        prompt: 'Hi',
        maxTokens: 5
      });
      return true;
    } catch (error) {
      return false;
    }
  }

  async generateCode(request: AIRequest): Promise<AIResponse> {
    if (!this.client) {
      throw new Error('Cohere client not initialized. Please set API key first.');
    }

    try {
      const prompt = this.buildPrompt(request);
      const enhancedPrompt = `You are an expert programmer. Generate clean, efficient, and well-commented code based on the following requirements:\n\n${prompt}`;

      const response = await this.client.generate({
        model: 'command',
        prompt: enhancedPrompt,
        maxTokens: request.maxTokens || 1000,
        temperature: request.temperature || 0.3,
        stopSequences: ['```', '\n\n---']
      });

      return {
        content: response.generations[0]?.text?.trim() || '',
        model: request.model,
        usage: {
          promptTokens: 0, // Cohere doesn't provide detailed token usage
          completionTokens: 0,
          totalTokens: 0
        },
        finishReason: 'stop'
      };
    } catch (error) {
      this.handleError(error);
    }
  }
}
