/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as serializers from "../index";
import * as Cohere from "../../api/index";
import * as core from "../../core";
import { ChatTextContent } from "./ChatTextContent";
export declare const SystemMessageV2ContentItem: core.serialization.Schema<serializers.SystemMessageV2ContentItem.Raw, Cohere.SystemMessageV2ContentItem>;
export declare namespace SystemMessageV2ContentItem {
    type Raw = SystemMessageV2ContentItem.Text;
    interface Text extends ChatTextContent.Raw {
        type: "text";
    }
}
