"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.BaseAIProvider = void 0;
class BaseAIProvider {
    apiKey = '';
    constructor(apiKey) {
        if (apiKey) {
            this.apiKey = apiKey;
        }
    }
    setApiKey(apiKey) {
        this.apiKey = apiKey;
    }
    validateApiKeyFormat(apiKey) {
        return Boolean(apiKey && apiKey.length > 0);
    }
    buildPrompt(request) {
        let prompt = '';
        if (request.context) {
            prompt += `Context:\n${request.context}\n\n`;
        }
        if (request.language) {
            prompt += `Language: ${request.language}\n\n`;
        }
        prompt += `Task: ${request.prompt}\n\n`;
        prompt += 'Please provide only the code without explanations unless specifically requested.';
        return prompt;
    }
    handleError(error) {
        if (error.response) {
            // API error response
            const status = error.response.status;
            const message = error.response.data?.error?.message || error.message;
            switch (status) {
                case 401:
                    throw new Error(`Authentication failed: ${message}. Please check your API key.`);
                case 403:
                    throw new Error(`Access forbidden: ${message}. Please check your API key permissions.`);
                case 429:
                    throw new Error(`Rate limit exceeded: ${message}. Please try again later.`);
                case 500:
                    throw new Error(`Server error: ${message}. Please try again later.`);
                default:
                    throw new Error(`API error (${status}): ${message}`);
            }
        }
        else if (error.code === 'ENOTFOUND' || error.code === 'ECONNREFUSED') {
            throw new Error('Network error: Unable to connect to AI service. Please check your internet connection.');
        }
        else {
            throw new Error(`Unexpected error: ${error.message}`);
        }
    }
}
exports.BaseAIProvider = BaseAIProvider;
//# sourceMappingURL=base.js.map