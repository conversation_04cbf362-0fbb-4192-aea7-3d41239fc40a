/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as serializers from "../index";
import * as Cohere from "../../api/index";
import * as core from "../../core";
import { Connector } from "./Connector";
export declare const ListConnectorsResponse: core.serialization.ObjectSchema<serializers.ListConnectorsResponse.Raw, Cohere.ListConnectorsResponse>;
export declare namespace ListConnectorsResponse {
    interface Raw {
        connectors: Connector.Raw[];
        total_count?: number | null;
    }
}
