import { GoogleGenerativeAI } from '@google/generative-ai';
import { BaseAIProvider } from './base';
import { AIRequest, AIResponse, AIModel } from '../types';

export class GoogleProvider extends BaseAIProvider {
  name = 'Google';
  models: AIModel[] = [
    {
      id: 'google-gemini',
      name: 'Gemini Pro',
      description: 'Google\'s advanced model for code and reasoning',
      provider: 'google',
      maxTokens: 2048,
      supportsStreaming: false
    },
    {
      id: 'google-palm',
      name: 'PaLM 2',
      description: 'Google\'s foundation model for various tasks',
      provider: 'google',
      maxTokens: 1024,
      supportsStreaming: false
    }
  ];

  private client: GoogleGenerativeAI | null = null;

  constructor(apiKey?: string) {
    super(apiKey);
    if (apiKey) {
      this.initializeClient(apiKey);
    }
  }

  private initializeClient(apiKey: string): void {
    this.client = new GoogleGenerativeAI(apiKey);
  }

  public setApiKey(apiKey: string): void {
    super.setApiKey(apiKey);
    this.initializeClient(apiKey);
  }

  async validateApiKey(apiKey: string): Promise<boolean> {
    try {
      const tempClient = new GoogleGenerativeAI(apiKey);
      const model = tempClient.getGenerativeModel({ model: 'gemini-pro' });
      await model.generateContent('Hi');
      return true;
    } catch (error) {
      return false;
    }
  }

  async generateCode(request: AIRequest): Promise<AIResponse> {
    if (!this.client) {
      throw new Error('Google client not initialized. Please set API key first.');
    }

    try {
      const modelName = request.model === 'google-palm' ? 'text-bison-001' : 'gemini-pro';
      const model = this.client.getGenerativeModel({ 
        model: modelName,
        generationConfig: {
          temperature: request.temperature || 0.3,
          maxOutputTokens: request.maxTokens || 1000,
        }
      });

      const prompt = this.buildPrompt(request);
      const enhancedPrompt = `You are an expert programmer. Generate clean, efficient, and well-commented code based on the following requirements:\n\n${prompt}`;

      const result = await model.generateContent(enhancedPrompt);
      const response = await result.response;
      const text = response.text();

      return {
        content: text.trim(),
        model: request.model,
        usage: {
          promptTokens: 0, // Google doesn't provide token usage in the response
          completionTokens: 0,
          totalTokens: 0
        },
        finishReason: 'stop'
      };
    } catch (error) {
      this.handleError(error);
    }
  }
}
