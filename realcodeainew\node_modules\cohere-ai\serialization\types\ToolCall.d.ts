/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as serializers from "../index";
import * as Cohere from "../../api/index";
import * as core from "../../core";
export declare const ToolCall: core.serialization.ObjectSchema<serializers.ToolCall.Raw, Cohere.ToolCall>;
export declare namespace ToolCall {
    interface Raw {
        name: string;
        parameters: Record<string, unknown>;
    }
}
