/**
 * This file was auto-generated by Fern from our API Definition.
 */
/**
 * @example
 *     {
 *         text: "tokenize me! :D",
 *         model: "command"
 *     }
 */
export interface TokenizeRequest {
    /** The string to be tokenized, the minimum text length is 1 character, and the maximum text length is 65536 characters. */
    text: string;
    /** The input will be tokenized by the tokenizer that is used by this model. */
    model: string;
}
