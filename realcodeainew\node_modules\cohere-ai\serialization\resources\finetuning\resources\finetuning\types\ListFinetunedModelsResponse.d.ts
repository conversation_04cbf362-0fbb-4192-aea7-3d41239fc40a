/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as serializers from "../../../../../index";
import * as Cohere from "../../../../../../api/index";
import * as core from "../../../../../../core";
import { FinetunedModel } from "./FinetunedModel";
export declare const ListFinetunedModelsResponse: core.serialization.ObjectSchema<serializers.finetuning.ListFinetunedModelsResponse.Raw, Cohere.finetuning.ListFinetunedModelsResponse>;
export declare namespace ListFinetunedModelsResponse {
    interface Raw {
        finetuned_models?: FinetunedModel.Raw[] | null;
        next_page_token?: string | null;
        total_size?: number | null;
    }
}
