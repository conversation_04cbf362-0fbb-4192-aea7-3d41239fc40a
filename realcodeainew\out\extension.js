"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.activate = activate;
exports.deactivate = deactivate;
const vscode = __importStar(require("vscode"));
const aiService_1 = require("./services/aiService");
const configService_1 = require("./services/configService");
const codeGenerationCommands_1 = require("./commands/codeGenerationCommands");
let aiService;
let configService;
let codeGenerationCommands;
async function activate(context) {
    console.log('Real Code AI extension is now active!');
    try {
        // Initialize services
        configService = new configService_1.ConfigService(context);
        aiService = new aiService_1.AIService();
        codeGenerationCommands = new codeGenerationCommands_1.CodeGenerationCommands(context, aiService, configService);
        // Register commands
        codeGenerationCommands.registerCommands();
        // Register configuration change listener
        context.subscriptions.push(vscode.workspace.onDidChangeConfiguration(event => {
            if (event.affectsConfiguration('realCodeAI')) {
                aiService.refreshConfiguration();
            }
        }));
        // Show welcome message on first activation
        const hasShownWelcome = context.globalState.get('realCodeAI.hasShownWelcome', false);
        if (!hasShownWelcome) {
            await showWelcomeMessage(context);
            await context.globalState.update('realCodeAI.hasShownWelcome', true);
        }
        // Check for configured API keys
        const config = await configService.getConfiguration();
        const hasApiKeys = Object.keys(config.apiKeys).length > 0;
        if (!hasApiKeys) {
            const result = await vscode.window.showInformationMessage('Welcome to Real Code AI! To get started, please configure your API keys.', 'Open Settings', 'Later');
            if (result === 'Open Settings') {
                vscode.commands.executeCommand('realCodeAI.openSettings');
            }
        }
        console.log('Real Code AI extension activated successfully!');
    }
    catch (error) {
        console.error('Failed to activate Real Code AI extension:', error);
        vscode.window.showErrorMessage(`Failed to activate Real Code AI: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
}
async function showWelcomeMessage(_context) {
    const message = `Welcome to Real Code AI! 🤖

This extension provides AI-powered code generation with support for multiple AI models including:
• OpenAI GPT-4 & GPT-3.5
• Anthropic Claude
• Google Gemini & PaLM
• Cohere Command
• Hugging Face CodeGen
• Mistral Codestral

Features:
• Generate code from natural language descriptions
• Explain existing code
• Refactor and optimize code
• Generate unit tests
• Context-aware suggestions

Get started by configuring your API keys in the settings.`;
    const result = await vscode.window.showInformationMessage(message, 'Open Settings', 'View Commands', 'OK');
    switch (result) {
        case 'Open Settings':
            vscode.commands.executeCommand('realCodeAI.openSettings');
            break;
        case 'View Commands':
            vscode.commands.executeCommand('workbench.action.showCommands');
            break;
    }
}
function deactivate() {
    console.log('Real Code AI extension deactivated');
}
//# sourceMappingURL=extension.js.map