/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as serializers from "../index";
import * as Cohere from "../../api/index";
import * as core from "../../core";
import { EmbedJob } from "./EmbedJob";
export declare const ListEmbedJobResponse: core.serialization.ObjectSchema<serializers.ListEmbedJobResponse.Raw, Cohere.ListEmbedJobResponse>;
export declare namespace ListEmbedJobResponse {
    interface Raw {
        embed_jobs?: EmbedJob.Raw[] | null;
    }
}
