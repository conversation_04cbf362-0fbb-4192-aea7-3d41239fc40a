"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.GoogleProvider = void 0;
const generative_ai_1 = require("@google/generative-ai");
const base_1 = require("./base");
class GoogleProvider extends base_1.BaseAIProvider {
    name = 'Google';
    models = [
        {
            id: 'google-gemini',
            name: 'Gemini Pro',
            description: 'Google\'s advanced model for code and reasoning',
            provider: 'google',
            maxTokens: 2048,
            supportsStreaming: false
        },
        {
            id: 'google-palm',
            name: 'PaLM 2',
            description: 'Google\'s foundation model for various tasks',
            provider: 'google',
            maxTokens: 1024,
            supportsStreaming: false
        }
    ];
    client = null;
    constructor(apiKey) {
        super(apiKey);
        if (apiKey) {
            this.initializeClient(apiKey);
        }
    }
    initializeClient(apiKey) {
        this.client = new generative_ai_1.GoogleGenerativeAI(apiKey);
    }
    setApiKey(apiKey) {
        super.setApiKey(apiKey);
        this.initializeClient(apiKey);
    }
    async validateApiKey(apiKey) {
        try {
            const tempClient = new generative_ai_1.GoogleGenerativeAI(apiKey);
            const model = tempClient.getGenerativeModel({ model: 'gemini-pro' });
            await model.generateContent('Hi');
            return true;
        }
        catch (error) {
            return false;
        }
    }
    async generateCode(request) {
        if (!this.client) {
            throw new Error('Google client not initialized. Please set API key first.');
        }
        try {
            const modelName = request.model === 'google-palm' ? 'text-bison-001' : 'gemini-pro';
            const model = this.client.getGenerativeModel({
                model: modelName,
                generationConfig: {
                    temperature: request.temperature || 0.3,
                    maxOutputTokens: request.maxTokens || 1000,
                }
            });
            const prompt = this.buildPrompt(request);
            const enhancedPrompt = `You are an expert programmer. Generate clean, efficient, and well-commented code based on the following requirements:\n\n${prompt}`;
            const result = await model.generateContent(enhancedPrompt);
            const response = await result.response;
            const text = response.text();
            return {
                content: text.trim(),
                model: request.model,
                usage: {
                    promptTokens: 0, // Google doesn't provide token usage in the response
                    completionTokens: 0,
                    totalTokens: 0
                },
                finishReason: 'stop'
            };
        }
        catch (error) {
            this.handleError(error);
        }
    }
}
exports.GoogleProvider = GoogleProvider;
//# sourceMappingURL=google.js.map