/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as serializers from "../index";
import * as Cohere from "../../api/index";
import * as core from "../../core";
export declare const RerankerDataMetrics: core.serialization.ObjectSchema<serializers.RerankerDataMetrics.Raw, Cohere.RerankerDataMetrics>;
export declare namespace RerankerDataMetrics {
    interface Raw {
        num_train_queries?: number | null;
        num_train_relevant_passages?: number | null;
        num_train_hard_negatives?: number | null;
        num_eval_queries?: number | null;
        num_eval_relevant_passages?: number | null;
        num_eval_hard_negatives?: number | null;
    }
}
