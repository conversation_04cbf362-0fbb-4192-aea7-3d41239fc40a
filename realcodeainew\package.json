{"name": "real-code-ai", "displayName": "Real Code AI", "description": "AI-powered code generation extension supporting multiple AI models including GPT-4, Claude, Gemini, and more", "version": "1.0.0", "publisher": "realcodeai", "engines": {"vscode": "^1.102.0"}, "categories": ["Machine Learning", "Snippets", "Other"], "keywords": ["ai", "code generation", "gpt", "claude", "gemini", "artificial intelligence", "autocomplete"], "activationEvents": [], "main": "./out/extension.js", "contributes": {"commands": [{"command": "realCodeAI.generateCode", "title": "Generate Code with AI", "category": "Real Code AI"}, {"command": "realCodeAI.generateCodeInline", "title": "Generate Code Inline", "category": "Real Code AI"}, {"command": "realCodeAI.openSettings", "title": "Open AI Settings", "category": "Real Code AI"}, {"command": "realCodeAI.explainCode", "title": "Explain Selected Code", "category": "Real Code AI"}, {"command": "realCodeAI.refactorCode", "title": "Refactor Selected Code", "category": "Real Code AI"}, {"command": "realCodeAI.generateTests", "title": "Generate Unit Tests", "category": "Real Code AI"}], "menus": {"editor/context": [{"command": "realCodeAI.generateCode", "group": "realCodeAI", "when": "editorTextFocus"}, {"command": "realCodeAI.explainCode", "group": "realCodeAI", "when": "editorHasSelection"}, {"command": "realCodeAI.refactorCode", "group": "realCodeAI", "when": "editorHasSelection"}], "commandPalette": [{"command": "realCodeAI.generateCode", "when": "editorTextFocus"}, {"command": "realCodeAI.generateCodeInline", "when": "editorTextFocus"}, {"command": "realCodeAI.openSettings"}, {"command": "realCodeAI.explainCode", "when": "editorHasSelection"}, {"command": "realCodeAI.refactorCode", "when": "editorHasSelection"}, {"command": "realCodeAI.generateTests", "when": "editorTextFocus"}]}, "keybindings": [{"command": "realCodeAI.generateCode", "key": "ctrl+shift+g", "mac": "cmd+shift+g", "when": "editorTextFocus"}, {"command": "realCodeAI.generateCodeInline", "key": "ctrl+shift+i", "mac": "cmd+shift+i", "when": "editorTextFocus"}], "configuration": {"title": "Real Code AI", "properties": {"realCodeAI.defaultModel": {"type": "string", "default": "openai-gpt4", "enum": ["openai-gpt4", "openai-gpt3.5", "anthropic-claude", "google-gemini", "openai-codex", "cohere-command", "huggingface-codegen", "mistral-codestral", "google-palm"], "enumDescriptions": ["OpenAI GPT-4 (Most capable, slower)", "OpenAI GPT-3.5 Turbo (Fast, good quality)", "<PERSON><PERSON><PERSON> (Excellent for code analysis)", "Google Gemini (Good for complex reasoning)", "OpenAI Codex (Specialized for code)", "Cohere Command (Good for text generation)", "Hugging Face CodeGen (Open source)", "Mistra<PERSON> Codestral (Code-specialized)", "Google PaLM (Google's foundation model)"], "description": "Default AI model to use for code generation"}, "realCodeAI.temperature": {"type": "number", "default": 0.3, "minimum": 0, "maximum": 2, "description": "Controls randomness in AI responses (0 = deterministic, 2 = very creative)"}, "realCodeAI.maxTokens": {"type": "number", "default": 1000, "minimum": 50, "maximum": 4000, "description": "Maximum number of tokens in AI response"}, "realCodeAI.includeContext": {"type": "boolean", "default": true, "description": "Include surrounding code as context for better AI responses"}, "realCodeAI.contextLines": {"type": "number", "default": 10, "minimum": 0, "maximum": 50, "description": "Number of lines above and below cursor to include as context"}, "realCodeAI.autoSave": {"type": "boolean", "default": false, "description": "Automatically save file after inserting generated code"}, "realCodeAI.showPreview": {"type": "boolean", "default": true, "description": "Show preview of generated code before insertion"}}}}, "scripts": {"vscode:prepublish": "npm run compile", "compile": "tsc -p ./", "watch": "tsc -watch -p ./", "pretest": "npm run compile && npm run lint", "lint": "eslint src --ext ts", "test": "vscode-test", "package": "npx vsce package"}, "dependencies": {"axios": "^1.6.0", "openai": "^4.20.0", "@anthropic-ai/sdk": "^0.9.0", "@google/generative-ai": "^0.2.0", "cohere-ai": "^7.0.0"}, "devDependencies": {"@types/vscode": "^1.102.0", "@types/mocha": "^10.0.10", "@types/node": "20.x", "@typescript-eslint/eslint-plugin": "^8.31.1", "@typescript-eslint/parser": "^8.31.1", "eslint": "^9.25.1", "typescript": "^5.8.3", "@vscode/test-cli": "^0.0.11", "@vscode/test-electron": "^2.5.2", "@vscode/vsce": "^2.22.0"}}