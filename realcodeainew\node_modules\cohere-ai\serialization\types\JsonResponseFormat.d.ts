/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as serializers from "../index";
import * as Cohere from "../../api/index";
import * as core from "../../core";
export declare const JsonResponseFormat: core.serialization.ObjectSchema<serializers.JsonResponseFormat.Raw, Cohere.JsonResponseFormat>;
export declare namespace JsonResponseFormat {
    interface Raw {
        schema?: Record<string, unknown> | null;
    }
}
