/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as serializers from "../index";
import * as Cohere from "../../api/index";
import * as core from "../../core";
import { ChatTextResponseFormatV2 } from "./ChatTextResponseFormatV2";
import { JsonResponseFormatV2 } from "./JsonResponseFormatV2";
export declare const ResponseFormatV2: core.serialization.Schema<serializers.ResponseFormatV2.Raw, Cohere.ResponseFormatV2>;
export declare namespace ResponseFormatV2 {
    type Raw = ResponseFormatV2.Text | ResponseFormatV2.JsonObject;
    interface Text extends ChatTextResponseFormatV2.Raw {
        type: "text";
    }
    interface JsonObject extends JsonResponseFormatV2.Raw {
        type: "json_object";
    }
}
