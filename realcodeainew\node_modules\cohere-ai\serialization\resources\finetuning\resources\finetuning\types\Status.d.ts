/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as serializers from "../../../../../index";
import * as Cohere from "../../../../../../api/index";
import * as core from "../../../../../../core";
export declare const Status: core.serialization.Schema<serializers.finetuning.Status.Raw, Cohere.finetuning.Status>;
export declare namespace Status {
    type Raw = "STATUS_UNSPECIFIED" | "STATUS_FINETUNING" | "STATUS_DEPLOYING_API" | "STATUS_READY" | "STATUS_FAILED" | "STATUS_DELETED" | "STATUS_TEMPORARILY_OFFLINE" | "STATUS_PAUSED" | "STATUS_QUEUED";
}
