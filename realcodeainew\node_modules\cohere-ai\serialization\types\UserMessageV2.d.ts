/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as serializers from "../index";
import * as Cohere from "../../api/index";
import * as core from "../../core";
import { UserMessageV2Content } from "./UserMessageV2Content";
export declare const UserMessageV2: core.serialization.ObjectSchema<serializers.UserMessageV2.Raw, Cohere.UserMessageV2>;
export declare namespace UserMessageV2 {
    interface Raw {
        content: UserMessageV2Content.Raw;
    }
}
