/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as serializers from "../index";
import * as Cohere from "../../api/index";
import * as core from "../../core";
export declare const UsageTokens: core.serialization.ObjectSchema<serializers.UsageTokens.Raw, Cohere.UsageTokens>;
export declare namespace UsageTokens {
    interface Raw {
        input_tokens?: number | null;
        output_tokens?: number | null;
    }
}
