/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as serializers from "../../../../index";
import * as Cohere from "../../../../../api/index";
import * as core from "../../../../../core";
import { ChatMessages } from "../../../../types/ChatMessages";
import { ToolV2 } from "../../../../types/ToolV2";
import { V2ChatStreamRequestDocumentsItem } from "../../types/V2ChatStreamRequestDocumentsItem";
import { CitationOptions } from "../../../../types/CitationOptions";
import { ResponseFormatV2 } from "../../../../types/ResponseFormatV2";
import { V2ChatStreamRequestSafetyMode } from "../../types/V2ChatStreamRequestSafetyMode";
import { V2ChatStreamRequestToolChoice } from "../../types/V2ChatStreamRequestToolChoice";
export declare const V2ChatStreamRequest: core.serialization.Schema<serializers.V2ChatStreamRequest.Raw, Cohere.V2ChatStreamRequest>;
export declare namespace V2ChatStreamRequest {
    interface Raw {
        model: string;
        messages: ChatMessages.Raw;
        tools?: ToolV2.Raw[] | null;
        strict_tools?: boolean | null;
        documents?: V2ChatStreamRequestDocumentsItem.Raw[] | null;
        citation_options?: CitationOptions.Raw | null;
        response_format?: ResponseFormatV2.Raw | null;
        safety_mode?: V2ChatStreamRequestSafetyMode.Raw | null;
        max_tokens?: number | null;
        stop_sequences?: string[] | null;
        temperature?: number | null;
        seed?: number | null;
        frequency_penalty?: number | null;
        presence_penalty?: number | null;
        k?: number | null;
        p?: number | null;
        logprobs?: boolean | null;
        tool_choice?: V2ChatStreamRequestToolChoice.Raw | null;
    }
}
