import OpenAI from 'openai';
import { BaseAIProvider } from './base';
import { AIRequest, AIResponse, AIModel } from '../types';

export class OpenAIProvider extends BaseAIProvider {
  name = 'OpenAI';
  models: AIModel[] = [
    {
      id: 'openai-gpt4',
      name: 'GPT-4',
      description: 'Most capable model, excellent for complex code generation',
      provider: 'openai',
      maxTokens: 8192,
      supportsStreaming: true
    },
    {
      id: 'openai-gpt3.5',
      name: 'GPT-3.5 Turbo',
      description: 'Fast and efficient, good for most coding tasks',
      provider: 'openai',
      maxTokens: 4096,
      supportsStreaming: true
    },
    {
      id: 'openai-codex',
      name: 'Codex',
      description: 'Specialized for code generation and completion',
      provider: 'openai',
      maxTokens: 4096,
      supportsStreaming: false
    }
  ];

  private client: OpenAI | null = null;

  constructor(apiKey?: string) {
    super(apiKey);
    if (apiKey) {
      this.initializeClient(apiKey);
    }
  }

  private initializeClient(apiKey: string): void {
    this.client = new OpenAI({
      apiKey: apiKey,
    });
  }

  public setApiKey(apiKey: string): void {
    super.setApiKey(apiKey);
    this.initializeClient(apiKey);
  }

  async validateApiKey(apiKey: string): Promise<boolean> {
    try {
      const tempClient = new OpenAI({ apiKey });
      await tempClient.models.list();
      return true;
    } catch (error) {
      return false;
    }
  }

  async generateCode(request: AIRequest): Promise<AIResponse> {
    if (!this.client) {
      throw new Error('OpenAI client not initialized. Please set API key first.');
    }

    try {
      const modelMap: { [key: string]: string } = {
        'openai-gpt4': 'gpt-4',
        'openai-gpt3.5': 'gpt-3.5-turbo',
        'openai-codex': 'code-davinci-002'
      };

      const openaiModel = modelMap[request.model] || 'gpt-3.5-turbo';
      const prompt = this.buildPrompt(request);

      if (request.model === 'openai-codex') {
        // Use completion API for Codex
        const response = await this.client.completions.create({
          model: openaiModel,
          prompt: prompt,
          max_tokens: request.maxTokens || 1000,
          temperature: request.temperature || 0.3,
          stop: ['\n\n', '```']
        });

        return {
          content: response.choices[0]?.text?.trim() || '',
          model: request.model,
          usage: {
            promptTokens: response.usage?.prompt_tokens || 0,
            completionTokens: response.usage?.completion_tokens || 0,
            totalTokens: response.usage?.total_tokens || 0
          },
          finishReason: response.choices[0]?.finish_reason || 'unknown'
        };
      } else {
        // Use chat completion API for GPT models
        const response = await this.client.chat.completions.create({
          model: openaiModel,
          messages: [
            {
              role: 'system',
              content: 'You are an expert programmer. Generate clean, efficient, and well-commented code based on the user\'s requirements.'
            },
            {
              role: 'user',
              content: prompt
            }
          ],
          max_tokens: request.maxTokens || 1000,
          temperature: request.temperature || 0.3
        });

        return {
          content: response.choices[0]?.message?.content?.trim() || '',
          model: request.model,
          usage: {
            promptTokens: response.usage?.prompt_tokens || 0,
            completionTokens: response.usage?.completion_tokens || 0,
            totalTokens: response.usage?.total_tokens || 0
          },
          finishReason: response.choices[0]?.finish_reason || 'unknown'
        };
      }
    } catch (error) {
      this.handleError(error);
    }
  }
}
