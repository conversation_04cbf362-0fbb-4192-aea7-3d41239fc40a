/**
 * This file was auto-generated by <PERSON>rn from our API Definition.
 */
import * as serializers from "../../../index";
import * as Cohere from "../../../../api/index";
import * as core from "../../../../core";
import { ChatMessageStartEvent } from "../../../types/ChatMessageStartEvent";
import { ChatContentStartEvent } from "../../../types/ChatContentStartEvent";
import { ChatContentDeltaEvent } from "../../../types/ChatContentDeltaEvent";
import { ChatContentEndEvent } from "../../../types/ChatContentEndEvent";
import { ChatToolPlanDeltaEvent } from "../../../types/ChatToolPlanDeltaEvent";
import { ChatToolCallStartEvent } from "../../../types/ChatToolCallStartEvent";
import { ChatToolCallDeltaEvent } from "../../../types/ChatToolCallDeltaEvent";
import { ChatToolCallEndEvent } from "../../../types/ChatToolCallEndEvent";
import { CitationStartEvent } from "../../../types/CitationStartEvent";
import { CitationEndEvent } from "../../../types/CitationEndEvent";
import { ChatMessageEndEvent } from "../../../types/ChatMessageEndEvent";
import { ChatDebugEvent } from "../../../types/ChatDebugEvent";
export declare const V2ChatStreamResponse: core.serialization.Schema<serializers.V2ChatStreamResponse.Raw, Cohere.V2ChatStreamResponse>;
export declare namespace V2ChatStreamResponse {
    type Raw = V2ChatStreamResponse.MessageStart | V2ChatStreamResponse.ContentStart | V2ChatStreamResponse.ContentDelta | V2ChatStreamResponse.ContentEnd | V2ChatStreamResponse.ToolPlanDelta | V2ChatStreamResponse.ToolCallStart | V2ChatStreamResponse.ToolCallDelta | V2ChatStreamResponse.ToolCallEnd | V2ChatStreamResponse.CitationStart | V2ChatStreamResponse.CitationEnd | V2ChatStreamResponse.MessageEnd | V2ChatStreamResponse.Debug;
    interface MessageStart extends ChatMessageStartEvent.Raw {
        type: "message-start";
    }
    interface ContentStart extends ChatContentStartEvent.Raw {
        type: "content-start";
    }
    interface ContentDelta extends ChatContentDeltaEvent.Raw {
        type: "content-delta";
    }
    interface ContentEnd extends ChatContentEndEvent.Raw {
        type: "content-end";
    }
    interface ToolPlanDelta extends ChatToolPlanDeltaEvent.Raw {
        type: "tool-plan-delta";
    }
    interface ToolCallStart extends ChatToolCallStartEvent.Raw {
        type: "tool-call-start";
    }
    interface ToolCallDelta extends ChatToolCallDeltaEvent.Raw {
        type: "tool-call-delta";
    }
    interface ToolCallEnd extends ChatToolCallEndEvent.Raw {
        type: "tool-call-end";
    }
    interface CitationStart extends CitationStartEvent.Raw {
        type: "citation-start";
    }
    interface CitationEnd extends CitationEndEvent.Raw {
        type: "citation-end";
    }
    interface MessageEnd extends ChatMessageEndEvent.Raw {
        type: "message-end";
    }
    interface Debug extends ChatDebugEvent.Raw {
        type: "debug";
    }
}
