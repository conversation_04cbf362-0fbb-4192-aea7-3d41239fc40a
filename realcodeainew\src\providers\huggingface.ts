import axios from 'axios';
import { BaseAIProvider } from './base';
import { AIRequest, AIResponse, AIModel } from '../types';

export class HuggingFaceProvider extends BaseAIProvider {
  name = 'Hugging Face';
  models: AIModel[] = [
    {
      id: 'huggingface-codegen',
      name: 'CodeGen',
      description: 'Open source code generation model',
      provider: 'huggingface',
      maxTokens: 2048,
      supportsStreaming: false
    }
  ];

  private readonly baseUrl = 'https://api-inference.huggingface.co/models';

  public setApiKey(apiKey: string): void {
    super.setApiKey(apiKey);
  }

  async validateApiKey(apiKey: string): Promise<boolean> {
    try {
      const response = await axios.post(
        `${this.baseUrl}/Salesforce/codegen-350M-mono`,
        { inputs: 'def hello():' },
        {
          headers: {
            'Authorization': `Bearer ${apiKey}`,
            'Content-Type': 'application/json'
          },
          timeout: 10000
        }
      );
      return response.status === 200;
    } catch (error) {
      return false;
    }
  }

  async generateCode(request: AIRequest): Promise<AIResponse> {
    if (!this.apiKey) {
      throw new Error('Hugging Face API key not set. Please set API key first.');
    }

    try {
      const prompt = this.buildPrompt(request);
      
      // Use CodeGen model for code generation
      const response = await axios.post(
        `${this.baseUrl}/Salesforce/codegen-350M-mono`,
        {
          inputs: prompt,
          parameters: {
            max_new_tokens: request.maxTokens || 1000,
            temperature: request.temperature || 0.3,
            do_sample: true,
            return_full_text: false
          }
        },
        {
          headers: {
            'Authorization': `Bearer ${this.apiKey}`,
            'Content-Type': 'application/json'
          },
          timeout: 30000
        }
      );

      const generatedText = response.data[0]?.generated_text || '';

      return {
        content: generatedText.trim(),
        model: request.model,
        usage: {
          promptTokens: 0, // HuggingFace doesn't provide token usage
          completionTokens: 0,
          totalTokens: 0
        },
        finishReason: 'stop'
      };
    } catch (error) {
      if (axios.isAxiosError(error)) {
        if (error.response?.status === 503) {
          throw new Error('Model is currently loading. Please try again in a few minutes.');
        }
      }
      this.handleError(error);
    }
  }
}
