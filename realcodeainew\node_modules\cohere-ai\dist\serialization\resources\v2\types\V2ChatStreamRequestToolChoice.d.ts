/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as serializers from "../../../index";
import * as Cohere from "../../../../api/index";
import * as core from "../../../../core";
export declare const V2ChatStreamRequestToolChoice: core.serialization.Schema<serializers.V2ChatStreamRequestToolChoice.Raw, Cohere.V2ChatStreamRequestToolChoice>;
export declare namespace V2ChatStreamRequestToolChoice {
    type Raw = "REQUIRED" | "NONE";
}
