/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as serializers from "../index";
import * as Cohere from "../../api/index";
import * as core from "../../core";
import { ChatToolSource } from "./ChatToolSource";
import { ChatDocumentSource } from "./ChatDocumentSource";
export declare const Source: core.serialization.Schema<serializers.Source.Raw, Cohere.Source>;
export declare namespace Source {
    type Raw = Source.Tool | Source.Document;
    interface Tool extends ChatToolSource.Raw {
        type: "tool";
    }
    interface Document extends ChatDocumentSource.Raw {
        type: "document";
    }
}
