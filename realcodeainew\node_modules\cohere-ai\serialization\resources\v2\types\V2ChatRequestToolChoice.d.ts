/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as serializers from "../../../index";
import * as Cohere from "../../../../api/index";
import * as core from "../../../../core";
export declare const V2ChatRequestToolChoice: core.serialization.Schema<serializers.V2ChatRequestToolChoice.Raw, Cohere.V2ChatRequestToolChoice>;
export declare namespace V2ChatRequestToolChoice {
    type Raw = "REQUIRED" | "NONE";
}
