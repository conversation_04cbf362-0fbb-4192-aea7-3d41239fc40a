import * as vscode from 'vscode';

export interface CodeContext {
  selectedText?: string;
  surroundingCode?: string;
  fileName?: string;
  language?: string;
  cursorPosition?: vscode.Position;
  lineNumber?: number;
  functionContext?: string;
  classContext?: string;
}

export class ContextService {
  
  public static extractContext(editor: vscode.TextEditor, contextLines: number = 10): CodeContext {
    const document = editor.document;
    const selection = editor.selection;
    const position = selection.active;
    
    const context: CodeContext = {
      fileName: document.fileName,
      language: document.languageId,
      cursorPosition: position,
      lineNumber: position.line + 1
    };

    // Get selected text if any
    if (!selection.isEmpty) {
      context.selectedText = document.getText(selection);
    }

    // Get surrounding code context
    const startLine = Math.max(0, position.line - contextLines);
    const endLine = Math.min(document.lineCount - 1, position.line + contextLines);
    const contextRange = new vscode.Range(startLine, 0, endLine, document.lineAt(endLine).text.length);
    context.surroundingCode = document.getText(contextRange);

    // Try to extract function context
    context.functionContext = this.extractFunctionContext(document, position);
    
    // Try to extract class context
    context.classContext = this.extractClassContext(document, position);

    return context;
  }

  private static extractFunctionContext(document: vscode.TextDocument, position: vscode.Position): string | undefined {
    const language = document.languageId;
    let functionPattern: RegExp;

    // Define function patterns for different languages
    switch (language) {
      case 'javascript':
      case 'typescript':
        functionPattern = /^\s*(function\s+\w+|const\s+\w+\s*=|let\s+\w+\s*=|var\s+\w+\s*=|\w+\s*:\s*function|\w+\s*\(.*\)\s*=>|\w+\s*\(.*\)\s*{)/;
        break;
      case 'python':
        functionPattern = /^\s*def\s+\w+\s*\(/;
        break;
      case 'java':
      case 'csharp':
        functionPattern = /^\s*(public|private|protected|static).*\s+\w+\s*\(/;
        break;
      case 'cpp':
      case 'c':
        functionPattern = /^\s*\w+\s+\w+\s*\(/;
        break;
      default:
        return undefined;
    }

    // Search backwards from current position to find function declaration
    for (let i = position.line; i >= 0; i--) {
      const line = document.lineAt(i);
      if (functionPattern.test(line.text)) {
        return line.text.trim();
      }
    }

    return undefined;
  }

  private static extractClassContext(document: vscode.TextDocument, position: vscode.Position): string | undefined {
    const language = document.languageId;
    let classPattern: RegExp;

    // Define class patterns for different languages
    switch (language) {
      case 'javascript':
      case 'typescript':
        classPattern = /^\s*class\s+\w+/;
        break;
      case 'python':
        classPattern = /^\s*class\s+\w+/;
        break;
      case 'java':
      case 'csharp':
        classPattern = /^\s*(public|private|protected)?\s*class\s+\w+/;
        break;
      case 'cpp':
        classPattern = /^\s*class\s+\w+/;
        break;
      default:
        return undefined;
    }

    // Search backwards from current position to find class declaration
    for (let i = position.line; i >= 0; i--) {
      const line = document.lineAt(i);
      if (classPattern.test(line.text)) {
        return line.text.trim();
      }
    }

    return undefined;
  }

  public static buildContextPrompt(context: CodeContext): string {
    let prompt = '';

    if (context.fileName) {
      prompt += `File: ${context.fileName}\n`;
    }

    if (context.language) {
      prompt += `Language: ${context.language}\n`;
    }

    if (context.lineNumber) {
      prompt += `Line: ${context.lineNumber}\n`;
    }

    if (context.classContext) {
      prompt += `Class: ${context.classContext}\n`;
    }

    if (context.functionContext) {
      prompt += `Function: ${context.functionContext}\n`;
    }

    if (context.selectedText) {
      prompt += `\nSelected code:\n${context.selectedText}\n`;
    }

    if (context.surroundingCode) {
      prompt += `\nSurrounding code:\n${context.surroundingCode}\n`;
    }

    return prompt;
  }

  public static getLanguageFromFileName(fileName: string): string {
    const extension = fileName.split('.').pop()?.toLowerCase();
    
    const languageMap: { [key: string]: string } = {
      'js': 'javascript',
      'jsx': 'javascript',
      'ts': 'typescript',
      'tsx': 'typescript',
      'py': 'python',
      'java': 'java',
      'cs': 'csharp',
      'cpp': 'cpp',
      'cc': 'cpp',
      'cxx': 'cpp',
      'c': 'c',
      'h': 'c',
      'hpp': 'cpp',
      'php': 'php',
      'rb': 'ruby',
      'go': 'go',
      'rs': 'rust',
      'swift': 'swift',
      'kt': 'kotlin',
      'scala': 'scala',
      'sh': 'bash',
      'ps1': 'powershell',
      'sql': 'sql',
      'html': 'html',
      'css': 'css',
      'scss': 'scss',
      'sass': 'sass',
      'less': 'less',
      'json': 'json',
      'xml': 'xml',
      'yaml': 'yaml',
      'yml': 'yaml',
      'md': 'markdown',
      'dockerfile': 'dockerfile'
    };

    return languageMap[extension || ''] || 'text';
  }

  public static formatCodeForInsertion(code: string, editor: vscode.TextEditor): string {
    const document = editor.document;
    const position = editor.selection.active;
    const line = document.lineAt(position.line);
    
    // Get the indentation of the current line
    const indentMatch = line.text.match(/^(\s*)/);
    const currentIndent = indentMatch ? indentMatch[1] : '';
    
    // Split the code into lines and apply indentation
    const lines = code.split('\n');
    const indentedLines = lines.map((codeLine, index) => {
      if (index === 0 && position.character > 0) {
        // First line: don't add extra indentation if cursor is not at line start
        return codeLine;
      }
      // Other lines: add current indentation
      return codeLine.trim() ? currentIndent + codeLine : codeLine;
    });
    
    return indentedLines.join('\n');
  }

  public static async insertCodeAtPosition(
    editor: vscode.TextEditor, 
    code: string, 
    position?: vscode.Position
  ): Promise<void> {
    const insertPosition = position || editor.selection.active;
    const formattedCode = this.formatCodeForInsertion(code, editor);
    
    await editor.edit(editBuilder => {
      editBuilder.insert(insertPosition, formattedCode);
    });
  }

  public static async replaceSelectedText(
    editor: vscode.TextEditor, 
    code: string
  ): Promise<void> {
    const selection = editor.selection;
    const formattedCode = this.formatCodeForInsertion(code, editor);
    
    await editor.edit(editBuilder => {
      if (selection.isEmpty) {
        editBuilder.insert(selection.active, formattedCode);
      } else {
        editBuilder.replace(selection, formattedCode);
      }
    });
  }
}
