/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as serializers from "../index";
import * as Cohere from "../../api/index";
import * as core from "../../core";
import { ChatStreamStartEvent } from "./ChatStreamStartEvent";
import { ChatSearchQueriesGenerationEvent } from "./ChatSearchQueriesGenerationEvent";
import { ChatSearchResultsEvent } from "./ChatSearchResultsEvent";
import { ChatTextGenerationEvent } from "./ChatTextGenerationEvent";
import { ChatCitationGenerationEvent } from "./ChatCitationGenerationEvent";
import { ChatToolCallsGenerationEvent } from "./ChatToolCallsGenerationEvent";
import { ChatStreamEndEvent } from "./ChatStreamEndEvent";
import { ChatToolCallsChunkEvent } from "./ChatToolCallsChunkEvent";
import { ChatDebugEvent } from "./ChatDebugEvent";
export declare const StreamedChatResponse: core.serialization.Schema<serializers.StreamedChatResponse.Raw, Cohere.StreamedChatResponse>;
export declare namespace StreamedChatResponse {
    type Raw = StreamedChatResponse.StreamStart | StreamedChatResponse.SearchQueriesGeneration | StreamedChatResponse.SearchResults | StreamedChatResponse.TextGeneration | StreamedChatResponse.CitationGeneration | StreamedChatResponse.ToolCallsGeneration | StreamedChatResponse.StreamEnd | StreamedChatResponse.ToolCallsChunk | StreamedChatResponse.Debug;
    interface StreamStart extends ChatStreamStartEvent.Raw {
        event_type: "stream-start";
    }
    interface SearchQueriesGeneration extends ChatSearchQueriesGenerationEvent.Raw {
        event_type: "search-queries-generation";
    }
    interface SearchResults extends ChatSearchResultsEvent.Raw {
        event_type: "search-results";
    }
    interface TextGeneration extends ChatTextGenerationEvent.Raw {
        event_type: "text-generation";
    }
    interface CitationGeneration extends ChatCitationGenerationEvent.Raw {
        event_type: "citation-generation";
    }
    interface ToolCallsGeneration extends ChatToolCallsGenerationEvent.Raw {
        event_type: "tool-calls-generation";
    }
    interface StreamEnd extends ChatStreamEndEvent.Raw {
        event_type: "stream-end";
    }
    interface ToolCallsChunk extends ChatToolCallsChunkEvent.Raw {
        event_type: "tool-calls-chunk";
    }
    interface Debug extends ChatDebugEvent.Raw {
        event_type: "debug";
    }
}
