/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as serializers from "../index";
import * as Cohere from "../../api/index";
import * as core from "../../core";
import { ToolCall } from "./ToolCall";
export declare const ToolResult: core.serialization.ObjectSchema<serializers.ToolResult.Raw, Cohere.ToolResult>;
export declare namespace ToolResult {
    interface Raw {
        call: ToolCall.Raw;
        outputs: Record<string, unknown>[];
    }
}
