/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as serializers from "../index";
import * as Cohere from "../../api/index";
import * as core from "../../core";
export declare const SummarizeRequestExtractiveness: core.serialization.Schema<serializers.SummarizeRequestExtractiveness.Raw, Cohere.SummarizeRequestExtractiveness>;
export declare namespace SummarizeRequestExtractiveness {
    type Raw = "low" | "medium" | "high";
}
