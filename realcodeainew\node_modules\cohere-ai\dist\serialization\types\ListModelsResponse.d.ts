/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as serializers from "../index";
import * as Cohere from "../../api/index";
import * as core from "../../core";
import { GetModelResponse } from "./GetModelResponse";
export declare const ListModelsResponse: core.serialization.ObjectSchema<serializers.ListModelsResponse.Raw, Cohere.ListModelsResponse>;
export declare namespace ListModelsResponse {
    interface Raw {
        models: GetModelResponse.Raw[];
        next_page_token?: string | null;
    }
}
