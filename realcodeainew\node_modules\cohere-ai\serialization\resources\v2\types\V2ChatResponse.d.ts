/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as serializers from "../../../index";
import * as Cohere from "../../../../api/index";
import * as core from "../../../../core";
import { ChatFinishReason } from "../../../types/ChatFinishReason";
import { AssistantMessageResponse } from "../../../types/AssistantMessageResponse";
import { Usage } from "../../../types/Usage";
import { LogprobItem } from "../../../types/LogprobItem";
export declare const V2ChatResponse: core.serialization.ObjectSchema<serializers.V2ChatResponse.Raw, Cohere.V2ChatResponse>;
export declare namespace V2ChatResponse {
    interface Raw {
        id: string;
        finish_reason: ChatFinishReason.Raw;
        message: AssistantMessageResponse.Raw;
        usage?: Usage.Raw | null;
        logprobs?: LogprobItem.Raw[] | null;
    }
}
