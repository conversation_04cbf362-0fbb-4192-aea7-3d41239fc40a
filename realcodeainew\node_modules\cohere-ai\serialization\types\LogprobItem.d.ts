/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as serializers from "../index";
import * as Cohere from "../../api/index";
import * as core from "../../core";
export declare const LogprobItem: core.serialization.ObjectSchema<serializers.LogprobItem.Raw, Cohere.LogprobItem>;
export declare namespace LogprobItem {
    interface Raw {
        text?: string | null;
        token_ids: number[];
        logprobs?: number[] | null;
    }
}
