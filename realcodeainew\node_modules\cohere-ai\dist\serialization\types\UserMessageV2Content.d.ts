/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as serializers from "../index";
import * as Cohere from "../../api/index";
import * as core from "../../core";
import { Content } from "./Content";
export declare const UserMessageV2Content: core.serialization.Schema<serializers.UserMessageV2Content.Raw, Cohere.UserMessageV2Content>;
export declare namespace UserMessageV2Content {
    type Raw = string | Content.Raw[];
}
