import { AIProvider, AIRequest, AIResponse, AIModel } from '../types';

export abstract class BaseAIProvider implements AIProvider {
  abstract name: string;
  abstract models: AIModel[];
  
  protected apiKey: string = '';

  constructor(apiKey?: string) {
    if (apiKey) {
      this.apiKey = apiKey;
    }
  }

  public setApiKey(apiKey: string): void {
    this.apiKey = apiKey;
  }

  protected validateApiKeyFormat(apiKey: string): boolean {
    return Boolean(apiKey && apiKey.length > 0);
  }

  abstract generateCode(request: AIRequest): Promise<AIResponse>;
  abstract validateApiKey(apiKey: string): Promise<boolean>;

  protected buildPrompt(request: AIRequest): string {
    let prompt = '';
    
    if (request.context) {
      prompt += `Context:\n${request.context}\n\n`;
    }
    
    if (request.language) {
      prompt += `Language: ${request.language}\n\n`;
    }
    
    prompt += `Task: ${request.prompt}\n\n`;
    prompt += 'Please provide only the code without explanations unless specifically requested.';
    
    return prompt;
  }

  protected handleError(error: any): never {
    if (error.response) {
      // API error response
      const status = error.response.status;
      const message = error.response.data?.error?.message || error.message;
      
      switch (status) {
        case 401:
          throw new Error(`Authentication failed: ${message}. Please check your API key.`);
        case 403:
          throw new Error(`Access forbidden: ${message}. Please check your API key permissions.`);
        case 429:
          throw new Error(`Rate limit exceeded: ${message}. Please try again later.`);
        case 500:
          throw new Error(`Server error: ${message}. Please try again later.`);
        default:
          throw new Error(`API error (${status}): ${message}`);
      }
    } else if (error.code === 'ENOTFOUND' || error.code === 'ECONNREFUSED') {
      throw new Error('Network error: Unable to connect to AI service. Please check your internet connection.');
    } else {
      throw new Error(`Unexpected error: ${error.message}`);
    }
  }
}
