import * as vscode from 'vscode';
import { AIService } from './services/aiService';
import { ConfigService } from './services/configService';
import { CodeGenerationCommands } from './commands/codeGenerationCommands';

let aiService: AIService;
let configService: ConfigService;
let codeGenerationCommands: CodeGenerationCommands;

export async function activate(context: vscode.ExtensionContext) {
	console.log('Real Code AI extension is now active!');

	try {
		// Initialize services
		configService = new ConfigService(context);
		aiService = new AIService();
		codeGenerationCommands = new CodeGenerationCommands(context, aiService, configService);

		// Register commands
		codeGenerationCommands.registerCommands();

		// Register configuration change listener
		context.subscriptions.push(
			vscode.workspace.onDidChangeConfiguration(event => {
				if (event.affectsConfiguration('realCodeAI')) {
					aiService.refreshConfiguration();
				}
			})
		);

		// Show welcome message on first activation
		const hasShownWelcome = context.globalState.get('realCodeAI.hasShownWelcome', false);
		if (!hasShownWelcome) {
			await showWelcomeMessage(context);
			await context.globalState.update('realCodeAI.hasShownWelcome', true);
		}

		// Check for configured API keys
		const config = await configService.getConfiguration();
		const hasApiKeys = Object.keys(config.apiKeys).length > 0;

		if (!hasApiKeys) {
			const result = await vscode.window.showInformationMessage(
				'Welcome to Real Code AI! To get started, please configure your API keys.',
				'Open Settings',
				'Later'
			);

			if (result === 'Open Settings') {
				vscode.commands.executeCommand('realCodeAI.openSettings');
			}
		}

		console.log('Real Code AI extension activated successfully!');

	} catch (error) {
		console.error('Failed to activate Real Code AI extension:', error);
		vscode.window.showErrorMessage(`Failed to activate Real Code AI: ${error instanceof Error ? error.message : 'Unknown error'}`);
	}
}

async function showWelcomeMessage(_context: vscode.ExtensionContext) {
	const message = `Welcome to Real Code AI! 🤖

This extension provides AI-powered code generation with support for multiple AI models including:
• OpenAI GPT-4 & GPT-3.5
• Anthropic Claude
• Google Gemini & PaLM
• Cohere Command
• Hugging Face CodeGen
• Mistral Codestral

Features:
• Generate code from natural language descriptions
• Explain existing code
• Refactor and optimize code
• Generate unit tests
• Context-aware suggestions

Get started by configuring your API keys in the settings.`;

	const result = await vscode.window.showInformationMessage(
		message,
		'Open Settings',
		'View Commands',
		'OK'
	);

	switch (result) {
		case 'Open Settings':
			vscode.commands.executeCommand('realCodeAI.openSettings');
			break;
		case 'View Commands':
			vscode.commands.executeCommand('workbench.action.showCommands');
			break;
	}
}

export function deactivate() {
	console.log('Real Code AI extension deactivated');
}
