import axios from 'axios';
import { BaseAIProvider } from './base';
import { AIRequest, AIResponse, AIModel } from '../types';

export class MistralProvider extends BaseAIProvider {
  name = 'Mistral';
  models: AIModel[] = [
    {
      id: 'mistral-codestral',
      name: 'Codestral',
      description: 'Mistra<PERSON>\'s specialized code model',
      provider: 'mistral',
      maxTokens: 2048,
      supportsStreaming: true
    }
  ];

  private readonly baseUrl = 'https://api.mistral.ai/v1';

  public setApiKey(apiKey: string): void {
    super.setApiKey(apiKey);
  }

  async validateApiKey(apiKey: string): Promise<boolean> {
    try {
      const response = await axios.get(`${this.baseUrl}/models`, {
        headers: {
          'Authorization': `Bearer ${apiKey}`,
          'Content-Type': 'application/json'
        },
        timeout: 10000
      });
      return response.status === 200;
    } catch (error) {
      return false;
    }
  }

  async generateCode(request: AIRequest): Promise<AIResponse> {
    if (!this.apiKey) {
      throw new Error('Mistral API key not set. Please set API key first.');
    }

    try {
      const prompt = this.buildPrompt(request);
      
      const response = await axios.post(
        `${this.baseUrl}/chat/completions`,
        {
          model: 'codestral-latest',
          messages: [
            {
              role: 'system',
              content: 'You are an expert programmer. Generate clean, efficient, and well-commented code based on the user\'s requirements.'
            },
            {
              role: 'user',
              content: prompt
            }
          ],
          max_tokens: request.maxTokens || 1000,
          temperature: request.temperature || 0.3
        },
        {
          headers: {
            'Authorization': `Bearer ${this.apiKey}`,
            'Content-Type': 'application/json'
          },
          timeout: 30000
        }
      );

      const choice = response.data.choices[0];
      const content = choice?.message?.content || '';

      return {
        content: content.trim(),
        model: request.model,
        usage: {
          promptTokens: response.data.usage?.prompt_tokens || 0,
          completionTokens: response.data.usage?.completion_tokens || 0,
          totalTokens: response.data.usage?.total_tokens || 0
        },
        finishReason: choice?.finish_reason || 'stop'
      };
    } catch (error) {
      this.handleError(error);
    }
  }
}
