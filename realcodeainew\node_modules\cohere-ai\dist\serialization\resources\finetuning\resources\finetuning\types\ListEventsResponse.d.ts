/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as serializers from "../../../../../index";
import * as Cohere from "../../../../../../api/index";
import * as core from "../../../../../../core";
import { Event } from "./Event";
export declare const ListEventsResponse: core.serialization.ObjectSchema<serializers.finetuning.ListEventsResponse.Raw, Cohere.finetuning.ListEventsResponse>;
export declare namespace ListEventsResponse {
    interface Raw {
        events?: Event.Raw[] | null;
        next_page_token?: string | null;
        total_size?: number | null;
    }
}
