/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as serializers from "../index";
import * as Cohere from "../../api/index";
import * as core from "../../core";
import { FinishReason } from "./FinishReason";
export declare const SingleGenerationInStream: core.serialization.ObjectSchema<serializers.SingleGenerationInStream.Raw, Cohere.SingleGenerationInStream>;
export declare namespace SingleGenerationInStream {
    interface Raw {
        id: string;
        text: string;
        index?: number | null;
        finish_reason: FinishReason.Raw;
    }
}
