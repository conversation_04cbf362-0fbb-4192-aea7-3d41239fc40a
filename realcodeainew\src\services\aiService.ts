import * as vscode from 'vscode';
import { AIProvider, AIRequest, AIResponse, AIModel, SUPPORTED_MODELS, ExtensionConfig, CodeGenerationRequest, CodeGenerationResult } from '../types';
import { OpenAIProvider } from '../providers/openai';
import { AnthropicProvider } from '../providers/anthropic';
import { GoogleProvider } from '../providers/google';
import { CohereProvider } from '../providers/cohere';
import { HuggingFaceProvider } from '../providers/huggingface';
import { MistralProvider } from '../providers/mistral';

export class AIService {
  private providers: Map<string, AIProvider> = new Map();
  private config: ExtensionConfig;

  constructor() {
    this.config = this.loadConfiguration();
    this.initializeProviders();
  }

  private loadConfiguration(): ExtensionConfig {
    const config = vscode.workspace.getConfiguration('realCodeAI');
    return {
      defaultModel: config.get('defaultModel', 'openai-gpt3.5'),
      temperature: config.get('temperature', 0.3),
      maxTokens: config.get('maxTokens', 1000),
      includeContext: config.get('includeContext', true),
      contextLines: config.get('contextLines', 10),
      autoSave: config.get('autoSave', false),
      showPreview: config.get('showPreview', true),
      apiKeys: this.loadApiKeys()
    };
  }

  private loadApiKeys(): { [provider: string]: string } {
    const keys: { [provider: string]: string } = {};
    const providers = ['openai', 'anthropic', 'google', 'cohere', 'huggingface', 'mistral'];
    
    for (const provider of providers) {
      const key = vscode.workspace.getConfiguration('realCodeAI').get(`apiKeys.${provider}`, '');
      if (key) {
        keys[provider] = key;
      }
    }
    
    return keys;
  }

  private initializeProviders(): void {
    // Initialize all providers
    this.providers.set('openai', new OpenAIProvider(this.config.apiKeys.openai));
    this.providers.set('anthropic', new AnthropicProvider(this.config.apiKeys.anthropic));
    this.providers.set('google', new GoogleProvider(this.config.apiKeys.google));
    this.providers.set('cohere', new CohereProvider(this.config.apiKeys.cohere));
    this.providers.set('huggingface', new HuggingFaceProvider(this.config.apiKeys.huggingface));
    this.providers.set('mistral', new MistralProvider(this.config.apiKeys.mistral));
  }

  public refreshConfiguration(): void {
    this.config = this.loadConfiguration();
    this.updateProviderApiKeys();
  }

  private updateProviderApiKeys(): void {
    for (const [providerName, provider] of this.providers) {
      const apiKey = this.config.apiKeys[providerName];
      if (apiKey) {
        provider.setApiKey(apiKey);
      }
    }
  }

  public getSupportedModels(): AIModel[] {
    return SUPPORTED_MODELS;
  }

  public getAvailableModels(): AIModel[] {
    return SUPPORTED_MODELS.filter(model => {
      const provider = this.providers.get(model.provider);
      return provider && this.config.apiKeys[model.provider];
    });
  }

  private getProviderForModel(modelId: string): AIProvider {
    const model = SUPPORTED_MODELS.find(m => m.id === modelId);
    if (!model) {
      throw new Error(`Unsupported model: ${modelId}`);
    }

    const provider = this.providers.get(model.provider);
    if (!provider) {
      throw new Error(`Provider not found for model: ${modelId}`);
    }

    if (!this.config.apiKeys[model.provider]) {
      throw new Error(`API key not configured for ${model.provider}. Please configure it in settings.`);
    }

    return provider;
  }

  public async validateApiKey(provider: string, apiKey: string): Promise<boolean> {
    const providerInstance = this.providers.get(provider);
    if (!providerInstance) {
      return false;
    }

    try {
      return await providerInstance.validateApiKey(apiKey);
    } catch (error) {
      console.error(`Error validating API key for ${provider}:`, error);
      return false;
    }
  }

  public async generateCode(request: CodeGenerationRequest): Promise<CodeGenerationResult> {
    const modelId = this.config.defaultModel;
    const provider = this.getProviderForModel(modelId);

    // Build context if enabled
    let context = '';
    if (this.config.includeContext && request.context) {
      context = request.context;
    }

    const aiRequest: AIRequest = {
      prompt: request.prompt,
      model: modelId,
      temperature: this.config.temperature,
      maxTokens: this.config.maxTokens,
      context: context,
      language: request.language
    };

    try {
      const response = await provider.generateCode(aiRequest);
      
      return {
        generatedCode: response.content,
        model: modelId,
        explanation: this.extractExplanation(response.content),
        suggestions: this.generateSuggestions(response.content),
        confidence: this.calculateConfidence(response)
      };
    } catch (error) {
      throw new Error(`Code generation failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  public async generateCodeWithModel(request: CodeGenerationRequest, modelId: string): Promise<CodeGenerationResult> {
    const provider = this.getProviderForModel(modelId);

    let context = '';
    if (this.config.includeContext && request.context) {
      context = request.context;
    }

    const aiRequest: AIRequest = {
      prompt: request.prompt,
      model: modelId,
      temperature: this.config.temperature,
      maxTokens: this.config.maxTokens,
      context: context,
      language: request.language
    };

    try {
      const response = await provider.generateCode(aiRequest);
      
      return {
        generatedCode: response.content,
        model: modelId,
        explanation: this.extractExplanation(response.content),
        suggestions: this.generateSuggestions(response.content),
        confidence: this.calculateConfidence(response)
      };
    } catch (error) {
      throw new Error(`Code generation failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  private extractExplanation(content: string): string | undefined {
    // Look for explanation patterns in the generated content
    const explanationPatterns = [
      /\/\*\*(.*?)\*\//s,
      /\/\/(.*?)$/m,
      /"""(.*?)"""/s,
      /'''(.*?)'''/s
    ];

    for (const pattern of explanationPatterns) {
      const match = content.match(pattern);
      if (match && match[1]) {
        return match[1].trim();
      }
    }

    return undefined;
  }

  private generateSuggestions(content: string): string[] {
    const suggestions: string[] = [];
    
    // Basic suggestions based on content analysis
    if (content.includes('TODO') || content.includes('FIXME')) {
      suggestions.push('Consider implementing the TODO/FIXME items');
    }
    
    if (content.includes('console.log') || content.includes('print(')) {
      suggestions.push('Remove debug statements before production');
    }
    
    if (content.length < 50) {
      suggestions.push('Consider adding more detailed implementation');
    }
    
    return suggestions;
  }

  private calculateConfidence(response: AIResponse): number {
    // Simple confidence calculation based on response characteristics
    let confidence = 0.8; // Base confidence
    
    if (response.finishReason === 'stop') {
      confidence += 0.1;
    }
    
    if (response.usage && response.usage.totalTokens > 0) {
      // Higher token usage might indicate more detailed response
      const tokenRatio = response.usage.completionTokens / response.usage.totalTokens;
      confidence += tokenRatio * 0.1;
    }
    
    return Math.min(confidence, 1.0);
  }
}
