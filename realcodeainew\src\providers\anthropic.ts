import Anthropic from '@anthropic-ai/sdk';
import { BaseAIProvider } from './base';
import { AIRequest, AIResponse, AIModel } from '../types';

export class AnthropicProvider extends BaseAIProvider {
  name = 'Anthropic';
  models: AIModel[] = [
    {
      id: 'anthropic-claude',
      name: '<PERSON> 3',
      description: 'Excellent for code analysis and complex reasoning',
      provider: 'anthropic',
      maxTokens: 4096,
      supportsStreaming: true
    }
  ];

  private client: Anthropic | null = null;

  constructor(apiKey?: string) {
    super(apiKey);
    if (apiKey) {
      this.initializeClient(apiKey);
    }
  }

  private initializeClient(apiKey: string): void {
    this.client = new Anthropic({
      apiKey: apiKey,
    });
  }

  public setApiKey(apiKey: string): void {
    super.setApiKey(apiKey);
    this.initializeClient(apiKey);
  }

  async validateApiKey(apiKey: string): Promise<boolean> {
    try {
      const tempClient = new Anthropic({ apiKey });
      // Try a minimal request to validate the key
      await tempClient.completions.create({
        model: 'claude-instant-1',
        max_tokens_to_sample: 10,
        prompt: '\n\nHuman: Hi\n\nAssistant:'
      });
      return true;
    } catch (error) {
      return false;
    }
  }

  async generateCode(request: AIRequest): Promise<AIResponse> {
    if (!this.client) {
      throw new Error('Anthropic client not initialized. Please set API key first.');
    }

    try {
      const prompt = this.buildPrompt(request);
      const fullPrompt = `\n\nHuman: You are an expert programmer. Generate clean, efficient, and well-commented code based on the following requirements:\n\n${prompt}\n\nAssistant:`;

      const response = await this.client.completions.create({
        model: 'claude-instant-1',
        max_tokens_to_sample: request.maxTokens || 1000,
        temperature: request.temperature || 0.3,
        prompt: fullPrompt
      });

      return {
        content: response.completion.trim(),
        model: request.model,
        usage: {
          promptTokens: 0, // Anthropic doesn't provide detailed token usage in legacy API
          completionTokens: 0,
          totalTokens: 0
        },
        finishReason: response.stop_reason || 'unknown'
      };
    } catch (error) {
      this.handleError(error);
    }
  }
}
