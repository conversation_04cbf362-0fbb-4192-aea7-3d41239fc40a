/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as serializers from "../../../index";
import * as Cohere from "../../../../api/index";
import * as core from "../../../../core";
export declare const V2ChatRequestSafetyMode: core.serialization.Schema<serializers.V2ChatRequestSafetyMode.Raw, Cohere.V2ChatRequestSafetyMode>;
export declare namespace V2ChatRequestSafetyMode {
    type Raw = "CONTEXTUAL" | "STRICT" | "OFF";
}
