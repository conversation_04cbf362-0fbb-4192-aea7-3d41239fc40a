/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as serializers from "../index";
import * as Cohere from "../../api/index";
import * as core from "../../core";
export declare const SummarizeRequestFormat: core.serialization.Schema<serializers.SummarizeRequestFormat.Raw, Cohere.SummarizeRequestFormat>;
export declare namespace SummarizeRequestFormat {
    type Raw = "paragraph" | "bullets";
}
