/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as serializers from "../index";
import * as Cohere from "../../api/index";
import * as core from "../../core";
import { ChatTextContent } from "./ChatTextContent";
import { DocumentContent } from "./DocumentContent";
export declare const ToolContent: core.serialization.Schema<serializers.ToolContent.Raw, Cohere.ToolContent>;
export declare namespace ToolContent {
    type Raw = ToolContent.Text | ToolContent.Document;
    interface Text extends ChatTextContent.Raw {
        type: "text";
    }
    interface Document extends DocumentContent.Raw {
        type: "document";
    }
}
