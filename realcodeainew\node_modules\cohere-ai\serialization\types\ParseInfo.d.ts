/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as serializers from "../index";
import * as Cohere from "../../api/index";
import * as core from "../../core";
export declare const ParseInfo: core.serialization.ObjectSchema<serializers.ParseInfo.Raw, Cohere.ParseInfo>;
export declare namespace ParseInfo {
    interface Raw {
        separator?: string | null;
        delimiter?: string | null;
    }
}
