/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as serializers from "../../../index";
import * as Cohere from "../../../../api/index";
import * as core from "../../../../core";
export declare const V2EmbedRequestTruncate: core.serialization.Schema<serializers.V2EmbedRequestTruncate.Raw, Cohere.V2EmbedRequestTruncate>;
export declare namespace V2EmbedRequestTruncate {
    type Raw = "NONE" | "START" | "END";
}
