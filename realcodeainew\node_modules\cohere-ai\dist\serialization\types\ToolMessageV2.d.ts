/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as serializers from "../index";
import * as Cohere from "../../api/index";
import * as core from "../../core";
import { ToolMessageV2Content } from "./ToolMessageV2Content";
export declare const ToolMessageV2: core.serialization.ObjectSchema<serializers.ToolMessageV2.Raw, Cohere.ToolMessageV2>;
export declare namespace ToolMessageV2 {
    interface Raw {
        tool_call_id: string;
        content: ToolMessageV2Content.Raw;
    }
}
