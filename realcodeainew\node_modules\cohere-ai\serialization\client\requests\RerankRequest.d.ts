/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as serializers from "../../index";
import * as Cohere from "../../../api/index";
import * as core from "../../../core";
import { RerankRequestDocumentsItem } from "../../types/RerankRequestDocumentsItem";
export declare const RerankRequest: core.serialization.Schema<serializers.RerankRequest.Raw, Cohere.RerankRequest>;
export declare namespace RerankRequest {
    interface Raw {
        model?: string | null;
        query: string;
        documents: RerankRequestDocumentsItem.Raw[];
        top_n?: number | null;
        rank_fields?: string[] | null;
        return_documents?: boolean | null;
        max_chunks_per_doc?: number | null;
    }
}
