/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as serializers from "../index";
import * as Cohere from "../../api/index";
import * as core from "../../core";
import { ChatMessage } from "./ChatMessage";
import { ChatToolMessage } from "./ChatToolMessage";
export declare const Message: core.serialization.Schema<serializers.Message.Raw, Cohere.Message>;
export declare namespace Message {
    type Raw = Message.Chatbot | Message.System | Message.User | Message.Tool;
    interface Chatbot extends ChatMessage.Raw {
        role: "CHATBOT";
    }
    interface System extends ChatMessage.Raw {
        role: "SYSTEM";
    }
    interface User extends ChatMessage.Raw {
        role: "USER";
    }
    interface Tool extends ChatToolMessage.Raw {
        role: "TOOL";
    }
}
