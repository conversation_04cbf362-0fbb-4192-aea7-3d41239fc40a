/**
 * This file was auto-generated by Fern from our API Definition.
 */
/**
 * The possible strategy used to serve a fine-tuned models.
 *
 *  - STRATEGY_UNSPECIFIED: Unspecified strategy.
 *  - STRATEGY_VANILLA: Deprecated: Serve the fine-tuned model on a dedicated GPU.
 *  - STRATEGY_TFEW: Deprecated: Serve the fine-tuned model on a shared GPU.
 */
export type Strategy = "STRATEGY_UNSPECIFIED" | "STRATEGY_VANILLA" | "STRATEGY_TFEW";
export declare const Strategy: {
    readonly StrategyUnspecified: "STRATEGY_UNSPECIFIED";
    readonly StrategyVanilla: "STRATEGY_VANILLA";
    readonly StrategyTfew: "STRATEGY_TFEW";
};
