/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as serializers from "../../../index";
import * as Cohere from "../../../../api/index";
import * as core from "../../../../core";
import { Document } from "../../../types/Document";
export declare const V2ChatStreamRequestDocumentsItem: core.serialization.Schema<serializers.V2ChatStreamRequestDocumentsItem.Raw, Cohere.V2ChatStreamRequestDocumentsItem>;
export declare namespace V2ChatStreamRequestDocumentsItem {
    type Raw = string | Document.Raw;
}
