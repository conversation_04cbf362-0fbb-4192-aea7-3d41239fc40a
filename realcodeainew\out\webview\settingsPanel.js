"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.SettingsPanel = void 0;
const vscode = __importStar(require("vscode"));
const types_1 = require("../types");
class SettingsPanel {
    configService;
    aiService;
    static currentPanel;
    _panel;
    _extensionUri;
    _disposables = [];
    static createOrShow(extensionUri, configService, aiService) {
        const column = vscode.window.activeTextEditor
            ? vscode.window.activeTextEditor.viewColumn
            : undefined;
        if (SettingsPanel.currentPanel) {
            SettingsPanel.currentPanel._panel.reveal(column);
            return;
        }
        const panel = vscode.window.createWebviewPanel('realCodeAISettings', 'Real Code AI Settings', column || vscode.ViewColumn.One, {
            enableScripts: true,
            localResourceRoots: [vscode.Uri.joinPath(extensionUri, 'media')]
        });
        SettingsPanel.currentPanel = new SettingsPanel(panel, extensionUri, configService, aiService);
    }
    constructor(panel, extensionUri, configService, aiService) {
        this.configService = configService;
        this.aiService = aiService;
        this._panel = panel;
        this._extensionUri = extensionUri;
        this._update();
        this._panel.onDidDispose(() => this.dispose(), null, this._disposables);
        this._panel.webview.onDidReceiveMessage(async (message) => {
            await this._handleMessage(message);
        }, null, this._disposables);
    }
    async _handleMessage(message) {
        switch (message.command) {
            case 'saveApiKey':
                await this._saveApiKey(message.provider, message.apiKey);
                break;
            case 'testApiKey':
                await this._testApiKey(message.provider, message.apiKey);
                break;
            case 'saveConfiguration':
                await this._saveConfiguration(message.config);
                break;
            case 'loadConfiguration':
                await this._loadConfiguration();
                break;
            case 'resetToDefaults':
                await this._resetToDefaults();
                break;
            case 'deleteApiKey':
                await this._deleteApiKey(message.provider);
                break;
        }
    }
    async _saveApiKey(provider, apiKey) {
        try {
            await this.configService.setApiKey(provider, apiKey);
            this.aiService.refreshConfiguration();
            this._panel.webview.postMessage({
                command: 'apiKeySaved',
                provider: provider,
                success: true
            });
            vscode.window.showInformationMessage(`API key for ${provider} saved successfully.`);
        }
        catch (error) {
            this._panel.webview.postMessage({
                command: 'apiKeySaved',
                provider: provider,
                success: false,
                error: error instanceof Error ? error.message : 'Unknown error'
            });
            vscode.window.showErrorMessage(`Failed to save API key: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }
    async _testApiKey(provider, apiKey) {
        try {
            const isValid = await this.aiService.validateApiKey(provider, apiKey);
            this._panel.webview.postMessage({
                command: 'apiKeyTested',
                provider: provider,
                isValid: isValid
            });
            if (isValid) {
                vscode.window.showInformationMessage(`API key for ${provider} is valid.`);
            }
            else {
                vscode.window.showWarningMessage(`API key for ${provider} is invalid.`);
            }
        }
        catch (error) {
            this._panel.webview.postMessage({
                command: 'apiKeyTested',
                provider: provider,
                isValid: false,
                error: error instanceof Error ? error.message : 'Unknown error'
            });
            vscode.window.showErrorMessage(`Failed to test API key: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }
    async _saveConfiguration(config) {
        try {
            const errors = this.configService.validateConfiguration(config);
            if (errors.length > 0) {
                throw new Error(errors.join(', '));
            }
            await this.configService.updateConfiguration(config);
            this.aiService.refreshConfiguration();
            this._panel.webview.postMessage({
                command: 'configurationSaved',
                success: true
            });
            vscode.window.showInformationMessage('Configuration saved successfully.');
        }
        catch (error) {
            this._panel.webview.postMessage({
                command: 'configurationSaved',
                success: false,
                error: error instanceof Error ? error.message : 'Unknown error'
            });
            vscode.window.showErrorMessage(`Failed to save configuration: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }
    async _loadConfiguration() {
        try {
            const config = await this.configService.getConfiguration();
            this._panel.webview.postMessage({
                command: 'configurationLoaded',
                config: config
            });
        }
        catch (error) {
            vscode.window.showErrorMessage(`Failed to load configuration: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }
    async _resetToDefaults() {
        try {
            await this.configService.resetToDefaults();
            this.aiService.refreshConfiguration();
            await this._loadConfiguration();
            vscode.window.showInformationMessage('Configuration reset to defaults.');
        }
        catch (error) {
            vscode.window.showErrorMessage(`Failed to reset configuration: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }
    async _deleteApiKey(provider) {
        try {
            await this.configService.deleteApiKey(provider);
            this.aiService.refreshConfiguration();
            this._panel.webview.postMessage({
                command: 'apiKeyDeleted',
                provider: provider,
                success: true
            });
            vscode.window.showInformationMessage(`API key for ${provider} deleted.`);
        }
        catch (error) {
            vscode.window.showErrorMessage(`Failed to delete API key: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }
    dispose() {
        SettingsPanel.currentPanel = undefined;
        this._panel.dispose();
        while (this._disposables.length) {
            const x = this._disposables.pop();
            if (x) {
                x.dispose();
            }
        }
    }
    async _update() {
        const webview = this._panel.webview;
        this._panel.title = 'Real Code AI Settings';
        this._panel.webview.html = await this._getHtmlForWebview(webview);
    }
    async _getHtmlForWebview(webview) {
        const config = await this.configService.getConfiguration();
        const models = types_1.SUPPORTED_MODELS;
        return `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Real Code AI Settings</title>
    <style>
        body {
            font-family: var(--vscode-font-family);
            font-size: var(--vscode-font-size);
            color: var(--vscode-foreground);
            background-color: var(--vscode-editor-background);
            padding: 20px;
            line-height: 1.6;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
        }
        .section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid var(--vscode-panel-border);
            border-radius: 6px;
            background-color: var(--vscode-panel-background);
        }
        .section h2 {
            margin-top: 0;
            color: var(--vscode-textLink-foreground);
            border-bottom: 1px solid var(--vscode-panel-border);
            padding-bottom: 10px;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input, select, textarea {
            width: 100%;
            padding: 8px;
            border: 1px solid var(--vscode-input-border);
            background-color: var(--vscode-input-background);
            color: var(--vscode-input-foreground);
            border-radius: 3px;
            font-family: inherit;
            font-size: inherit;
        }
        input:focus, select:focus, textarea:focus {
            outline: none;
            border-color: var(--vscode-focusBorder);
        }
        button {
            background-color: var(--vscode-button-background);
            color: var(--vscode-button-foreground);
            border: none;
            padding: 8px 16px;
            border-radius: 3px;
            cursor: pointer;
            margin-right: 10px;
            margin-bottom: 10px;
        }
        button:hover {
            background-color: var(--vscode-button-hoverBackground);
        }
        button.secondary {
            background-color: var(--vscode-button-secondaryBackground);
            color: var(--vscode-button-secondaryForeground);
        }
        button.secondary:hover {
            background-color: var(--vscode-button-secondaryHoverBackground);
        }
        .api-key-section {
            border: 1px solid var(--vscode-panel-border);
            border-radius: 6px;
            padding: 15px;
            margin-bottom: 15px;
            background-color: var(--vscode-editor-background);
        }
        .api-key-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }
        .status-indicator {
            padding: 4px 8px;
            border-radius: 3px;
            font-size: 12px;
            font-weight: bold;
        }
        .status-valid {
            background-color: var(--vscode-testing-iconPassed);
            color: white;
        }
        .status-invalid {
            background-color: var(--vscode-testing-iconFailed);
            color: white;
        }
        .status-unknown {
            background-color: var(--vscode-badge-background);
            color: var(--vscode-badge-foreground);
        }
        .description {
            font-size: 12px;
            color: var(--vscode-descriptionForeground);
            margin-bottom: 10px;
        }
        .button-group {
            display: flex;
            gap: 10px;
            margin-top: 10px;
        }
        .range-input {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        .range-input input[type="range"] {
            flex: 1;
        }
        .range-value {
            min-width: 50px;
            text-align: center;
            font-weight: bold;
        }
        .checkbox-group {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        .checkbox-group input[type="checkbox"] {
            width: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Real Code AI Settings</h1>
        
        <div class="section">
            <h2>API Keys Configuration</h2>
            <p class="description">Configure API keys for different AI providers. Keys are stored securely in VS Code's secret storage.</p>
            
            ${this._generateApiKeysSections(models, config)}
        </div>

        <div class="section">
            <h2>General Configuration</h2>
            
            <div class="form-group">
                <label for="defaultModel">Default AI Model:</label>
                <select id="defaultModel">
                    ${models.map(model => `<option value="${model.id}" ${config.defaultModel === model.id ? 'selected' : ''}>
                            ${model.name} (${model.provider}) - ${model.description}
                        </option>`).join('')}
                </select>
            </div>

            <div class="form-group">
                <label for="temperature">Temperature (Creativity):</label>
                <div class="range-input">
                    <input type="range" id="temperature" min="0" max="2" step="0.1" value="${config.temperature}">
                    <span class="range-value" id="temperatureValue">${config.temperature}</span>
                </div>
                <div class="description">Lower values = more deterministic, Higher values = more creative</div>
            </div>

            <div class="form-group">
                <label for="maxTokens">Max Tokens:</label>
                <div class="range-input">
                    <input type="range" id="maxTokens" min="50" max="4000" step="50" value="${config.maxTokens}">
                    <span class="range-value" id="maxTokensValue">${config.maxTokens}</span>
                </div>
                <div class="description">Maximum length of AI response</div>
            </div>

            <div class="form-group">
                <div class="checkbox-group">
                    <input type="checkbox" id="includeContext" ${config.includeContext ? 'checked' : ''}>
                    <label for="includeContext">Include surrounding code as context</label>
                </div>
            </div>

            <div class="form-group">
                <label for="contextLines">Context Lines:</label>
                <div class="range-input">
                    <input type="range" id="contextLines" min="0" max="50" step="1" value="${config.contextLines}">
                    <span class="range-value" id="contextLinesValue">${config.contextLines}</span>
                </div>
                <div class="description">Number of lines above and below cursor to include as context</div>
            </div>

            <div class="form-group">
                <div class="checkbox-group">
                    <input type="checkbox" id="autoSave" ${config.autoSave ? 'checked' : ''}>
                    <label for="autoSave">Auto-save file after inserting generated code</label>
                </div>
            </div>

            <div class="form-group">
                <div class="checkbox-group">
                    <input type="checkbox" id="showPreview" ${config.showPreview ? 'checked' : ''}>
                    <label for="showPreview">Show preview before inserting code</label>
                </div>
            </div>

            <div class="button-group">
                <button onclick="saveConfiguration()">Save Configuration</button>
                <button class="secondary" onclick="resetToDefaults()">Reset to Defaults</button>
            </div>
        </div>
    </div>

    <script>
        const vscode = acquireVsCodeApi();

        // Update range value displays
        document.getElementById('temperature').addEventListener('input', function() {
            document.getElementById('temperatureValue').textContent = this.value;
        });

        document.getElementById('maxTokens').addEventListener('input', function() {
            document.getElementById('maxTokensValue').textContent = this.value;
        });

        document.getElementById('contextLines').addEventListener('input', function() {
            document.getElementById('contextLinesValue').textContent = this.value;
        });

        function saveApiKey(provider) {
            const apiKey = document.getElementById(provider + 'ApiKey').value;
            vscode.postMessage({
                command: 'saveApiKey',
                provider: provider,
                apiKey: apiKey
            });
        }

        function testApiKey(provider) {
            const apiKey = document.getElementById(provider + 'ApiKey').value;
            if (!apiKey.trim()) {
                alert('Please enter an API key first.');
                return;
            }
            vscode.postMessage({
                command: 'testApiKey',
                provider: provider,
                apiKey: apiKey
            });
        }

        function deleteApiKey(provider) {
            if (confirm('Are you sure you want to delete this API key?')) {
                vscode.postMessage({
                    command: 'deleteApiKey',
                    provider: provider
                });
                document.getElementById(provider + 'ApiKey').value = '';
            }
        }

        function saveConfiguration() {
            const config = {
                defaultModel: document.getElementById('defaultModel').value,
                temperature: parseFloat(document.getElementById('temperature').value),
                maxTokens: parseInt(document.getElementById('maxTokens').value),
                includeContext: document.getElementById('includeContext').checked,
                contextLines: parseInt(document.getElementById('contextLines').value),
                autoSave: document.getElementById('autoSave').checked,
                showPreview: document.getElementById('showPreview').checked
            };

            vscode.postMessage({
                command: 'saveConfiguration',
                config: config
            });
        }

        function resetToDefaults() {
            if (confirm('Are you sure you want to reset all settings to defaults?')) {
                vscode.postMessage({
                    command: 'resetToDefaults'
                });
            }
        }

        // Handle messages from extension
        window.addEventListener('message', event => {
            const message = event.data;
            switch (message.command) {
                case 'configurationLoaded':
                    // Update UI with loaded configuration
                    break;
            }
        });

        // Load configuration on startup
        vscode.postMessage({ command: 'loadConfiguration' });
    </script>
</body>
</html>`;
    }
    _generateApiKeysSections(models, config) {
        const providers = [...new Set(models.map(m => m.provider))];
        return providers.map(provider => {
            const providerModels = models.filter(m => m.provider === provider);
            const hasApiKey = config.apiKeys[provider];
            return `
        <div class="api-key-section">
            <div class="api-key-header">
                <h3>${provider.charAt(0).toUpperCase() + provider.slice(1)}</h3>
                <span class="status-indicator ${hasApiKey ? 'status-valid' : 'status-unknown'}">
                    ${hasApiKey ? 'Configured' : 'Not Configured'}
                </span>
            </div>
            <div class="description">
                Models: ${providerModels.map(m => m.name).join(', ')}
            </div>
            <div class="form-group">
                <label for="${provider}ApiKey">API Key:</label>
                <input type="password" id="${provider}ApiKey" placeholder="Enter your ${provider} API key">
            </div>
            <div class="button-group">
                <button onclick="saveApiKey('${provider}')">Save</button>
                <button class="secondary" onclick="testApiKey('${provider}')">Test</button>
                <button class="secondary" onclick="deleteApiKey('${provider}')">Delete</button>
            </div>
        </div>
      `;
        }).join('');
    }
}
exports.SettingsPanel = SettingsPanel;
//# sourceMappingURL=settingsPanel.js.map