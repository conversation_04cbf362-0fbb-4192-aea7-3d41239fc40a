/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as serializers from "../../../index";
import * as Cohere from "../../../../api/index";
import * as core from "../../../../core";
export declare const V2ChatStreamRequestSafetyMode: core.serialization.Schema<serializers.V2ChatStreamRequestSafetyMode.Raw, Cohere.V2ChatStreamRequestSafetyMode>;
export declare namespace V2ChatStreamRequestSafetyMode {
    type Raw = "CONTEXTUAL" | "STRICT" | "OFF";
}
