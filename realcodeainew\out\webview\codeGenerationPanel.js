"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.CodeGenerationPanel = void 0;
const vscode = __importStar(require("vscode"));
const contextService_1 = require("../services/contextService");
class CodeGenerationPanel {
    aiService;
    initialPrompt;
    static currentPanel;
    _panel;
    _extensionUri;
    _disposables = [];
    _currentContext;
    static createOrShow(extensionUri, aiService, context, initialPrompt) {
        const column = vscode.window.activeTextEditor
            ? vscode.window.activeTextEditor.viewColumn
            : undefined;
        if (CodeGenerationPanel.currentPanel) {
            CodeGenerationPanel.currentPanel._panel.reveal(column);
            if (context) {
                CodeGenerationPanel.currentPanel._currentContext = context;
                CodeGenerationPanel.currentPanel._update();
            }
            return;
        }
        const panel = vscode.window.createWebviewPanel('realCodeAIGeneration', 'Real Code AI - Generate Code', column || vscode.ViewColumn.Beside, {
            enableScripts: true,
            localResourceRoots: [vscode.Uri.joinPath(extensionUri, 'media')]
        });
        CodeGenerationPanel.currentPanel = new CodeGenerationPanel(panel, extensionUri, aiService, context, initialPrompt);
    }
    constructor(panel, extensionUri, aiService, context, initialPrompt) {
        this.aiService = aiService;
        this.initialPrompt = initialPrompt;
        this._panel = panel;
        this._extensionUri = extensionUri;
        this._currentContext = context;
        this._update();
        this._panel.onDidDispose(() => this.dispose(), null, this._disposables);
        this._panel.webview.onDidReceiveMessage(async (message) => {
            await this._handleMessage(message);
        }, null, this._disposables);
    }
    async _handleMessage(message) {
        switch (message.command) {
            case 'generateCode':
                await this._generateCode(message.prompt, message.model);
                break;
            case 'insertCode':
                await this._insertCode(message.code);
                break;
            case 'replaceCode':
                await this._replaceCode(message.code);
                break;
            case 'copyCode':
                await this._copyCode(message.code);
                break;
            case 'refreshContext':
                await this._refreshContext();
                break;
        }
    }
    async _generateCode(prompt, modelId) {
        if (!prompt.trim()) {
            vscode.window.showWarningMessage('Please enter a prompt for code generation.');
            return;
        }
        this._panel.webview.postMessage({
            command: 'generationStarted'
        });
        try {
            const request = {
                prompt: prompt,
                selectedText: this._currentContext?.selectedText,
                fileName: this._currentContext?.fileName,
                language: this._currentContext?.language,
                context: this._currentContext ? contextService_1.ContextService.buildContextPrompt(this._currentContext) : undefined
            };
            let result;
            if (modelId) {
                result = await this.aiService.generateCodeWithModel(request, modelId);
            }
            else {
                result = await this.aiService.generateCode(request);
            }
            this._panel.webview.postMessage({
                command: 'generationCompleted',
                result: result
            });
        }
        catch (error) {
            this._panel.webview.postMessage({
                command: 'generationFailed',
                error: error instanceof Error ? error.message : 'Unknown error'
            });
            vscode.window.showErrorMessage(`Code generation failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }
    async _insertCode(code) {
        const editor = vscode.window.activeTextEditor;
        if (!editor) {
            vscode.window.showWarningMessage('No active editor found.');
            return;
        }
        try {
            await contextService_1.ContextService.insertCodeAtPosition(editor, code);
            vscode.window.showInformationMessage('Code inserted successfully.');
            // Auto-save if enabled
            const config = vscode.workspace.getConfiguration('realCodeAI');
            if (config.get('autoSave', false)) {
                await editor.document.save();
            }
        }
        catch (error) {
            vscode.window.showErrorMessage(`Failed to insert code: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }
    async _replaceCode(code) {
        const editor = vscode.window.activeTextEditor;
        if (!editor) {
            vscode.window.showWarningMessage('No active editor found.');
            return;
        }
        try {
            await contextService_1.ContextService.replaceSelectedText(editor, code);
            vscode.window.showInformationMessage('Code replaced successfully.');
            // Auto-save if enabled
            const config = vscode.workspace.getConfiguration('realCodeAI');
            if (config.get('autoSave', false)) {
                await editor.document.save();
            }
        }
        catch (error) {
            vscode.window.showErrorMessage(`Failed to replace code: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }
    async _copyCode(code) {
        try {
            await vscode.env.clipboard.writeText(code);
            vscode.window.showInformationMessage('Code copied to clipboard.');
        }
        catch (error) {
            vscode.window.showErrorMessage(`Failed to copy code: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }
    async _refreshContext() {
        const editor = vscode.window.activeTextEditor;
        if (editor) {
            const config = vscode.workspace.getConfiguration('realCodeAI');
            const contextLines = config.get('contextLines', 10);
            this._currentContext = contextService_1.ContextService.extractContext(editor, contextLines);
            this._update();
        }
    }
    dispose() {
        CodeGenerationPanel.currentPanel = undefined;
        this._panel.dispose();
        while (this._disposables.length) {
            const x = this._disposables.pop();
            if (x) {
                x.dispose();
            }
        }
    }
    async _update() {
        const webview = this._panel.webview;
        this._panel.title = 'Real Code AI - Generate Code';
        this._panel.webview.html = await this._getHtmlForWebview(webview);
    }
    async _getHtmlForWebview(webview) {
        const availableModels = this.aiService.getAvailableModels();
        const contextInfo = this._currentContext ? this._getContextInfo() : '';
        return `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Real Code AI - Generate Code</title>
    <style>
        body {
            font-family: var(--vscode-font-family);
            font-size: var(--vscode-font-size);
            color: var(--vscode-foreground);
            background-color: var(--vscode-editor-background);
            padding: 20px;
            line-height: 1.6;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
        }
        .section {
            margin-bottom: 20px;
            padding: 15px;
            border: 1px solid var(--vscode-panel-border);
            border-radius: 6px;
            background-color: var(--vscode-panel-background);
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        textarea, select, input {
            width: 100%;
            padding: 8px;
            border: 1px solid var(--vscode-input-border);
            background-color: var(--vscode-input-background);
            color: var(--vscode-input-foreground);
            border-radius: 3px;
            font-family: inherit;
            font-size: inherit;
        }
        textarea {
            resize: vertical;
            min-height: 100px;
        }
        button {
            background-color: var(--vscode-button-background);
            color: var(--vscode-button-foreground);
            border: none;
            padding: 8px 16px;
            border-radius: 3px;
            cursor: pointer;
            margin-right: 10px;
            margin-bottom: 10px;
        }
        button:hover {
            background-color: var(--vscode-button-hoverBackground);
        }
        button:disabled {
            opacity: 0.6;
            cursor: not-allowed;
        }
        button.secondary {
            background-color: var(--vscode-button-secondaryBackground);
            color: var(--vscode-button-secondaryForeground);
        }
        button.secondary:hover {
            background-color: var(--vscode-button-secondaryHoverBackground);
        }
        .code-output {
            background-color: var(--vscode-textCodeBlock-background);
            border: 1px solid var(--vscode-panel-border);
            border-radius: 3px;
            padding: 15px;
            font-family: var(--vscode-editor-font-family);
            font-size: var(--vscode-editor-font-size);
            white-space: pre-wrap;
            overflow-x: auto;
            max-height: 400px;
            overflow-y: auto;
        }
        .context-info {
            background-color: var(--vscode-editor-background);
            border: 1px solid var(--vscode-panel-border);
            border-radius: 3px;
            padding: 10px;
            font-size: 12px;
            color: var(--vscode-descriptionForeground);
        }
        .loading {
            display: none;
            text-align: center;
            padding: 20px;
        }
        .loading.show {
            display: block;
        }
        .result-section {
            display: none;
        }
        .result-section.show {
            display: block;
        }
        .button-group {
            display: flex;
            gap: 10px;
            margin-top: 15px;
            flex-wrap: wrap;
        }
        .model-info {
            font-size: 12px;
            color: var(--vscode-descriptionForeground);
            margin-top: 5px;
        }
        .error {
            color: var(--vscode-errorForeground);
            background-color: var(--vscode-inputValidation-errorBackground);
            border: 1px solid var(--vscode-inputValidation-errorBorder);
            padding: 10px;
            border-radius: 3px;
            margin-top: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Generate Code with AI</h1>
        
        <div class="section">
            <div class="form-group">
                <label for="prompt">Describe what code you want to generate:</label>
                <textarea id="prompt" placeholder="e.g., Create a function that sorts an array of objects by a specific property">${this.initialPrompt || ''}</textarea>
            </div>
            
            <div class="form-group">
                <label for="model">AI Model:</label>
                <select id="model">
                    <option value="">Use default model</option>
                    ${availableModels.map(model => `<option value="${model.id}">${model.name} (${model.provider})</option>`).join('')}
                </select>
                <div class="model-info">
                    Available models: ${availableModels.length} configured
                </div>
            </div>
            
            <button onclick="generateCode()" id="generateBtn">Generate Code</button>
            <button class="secondary" onclick="refreshContext()">Refresh Context</button>
        </div>

        ${contextInfo}

        <div class="loading" id="loading">
            <p>🤖 Generating code...</p>
        </div>

        <div class="result-section" id="resultSection">
            <div class="section">
                <h3>Generated Code</h3>
                <div class="code-output" id="generatedCode"></div>
                <div class="button-group">
                    <button onclick="insertCode()">Insert at Cursor</button>
                    <button onclick="replaceCode()">Replace Selection</button>
                    <button class="secondary" onclick="copyCode()">Copy to Clipboard</button>
                </div>
                <div id="explanation" style="margin-top: 15px;"></div>
                <div id="suggestions" style="margin-top: 10px;"></div>
            </div>
        </div>

        <div id="error" class="error" style="display: none;"></div>
    </div>

    <script>
        const vscode = acquireVsCodeApi();
        let currentGeneratedCode = '';

        function generateCode() {
            const prompt = document.getElementById('prompt').value;
            const model = document.getElementById('model').value;
            
            if (!prompt.trim()) {
                alert('Please enter a prompt for code generation.');
                return;
            }

            vscode.postMessage({
                command: 'generateCode',
                prompt: prompt,
                model: model || undefined
            });
        }

        function insertCode() {
            if (currentGeneratedCode) {
                vscode.postMessage({
                    command: 'insertCode',
                    code: currentGeneratedCode
                });
            }
        }

        function replaceCode() {
            if (currentGeneratedCode) {
                vscode.postMessage({
                    command: 'replaceCode',
                    code: currentGeneratedCode
                });
            }
        }

        function copyCode() {
            if (currentGeneratedCode) {
                vscode.postMessage({
                    command: 'copyCode',
                    code: currentGeneratedCode
                });
            }
        }

        function refreshContext() {
            vscode.postMessage({
                command: 'refreshContext'
            });
        }

        // Handle messages from extension
        window.addEventListener('message', event => {
            const message = event.data;
            switch (message.command) {
                case 'generationStarted':
                    document.getElementById('loading').classList.add('show');
                    document.getElementById('resultSection').classList.remove('show');
                    document.getElementById('error').style.display = 'none';
                    document.getElementById('generateBtn').disabled = true;
                    break;
                    
                case 'generationCompleted':
                    document.getElementById('loading').classList.remove('show');
                    document.getElementById('resultSection').classList.add('show');
                    document.getElementById('generateBtn').disabled = false;
                    
                    currentGeneratedCode = message.result.generatedCode;
                    document.getElementById('generatedCode').textContent = currentGeneratedCode;
                    
                    if (message.result.explanation) {
                        document.getElementById('explanation').innerHTML = 
                            '<strong>Explanation:</strong> ' + message.result.explanation;
                    }
                    
                    if (message.result.suggestions && message.result.suggestions.length > 0) {
                        document.getElementById('suggestions').innerHTML = 
                            '<strong>Suggestions:</strong> ' + message.result.suggestions.join(', ');
                    }
                    break;
                    
                case 'generationFailed':
                    document.getElementById('loading').classList.remove('show');
                    document.getElementById('generateBtn').disabled = false;
                    document.getElementById('error').style.display = 'block';
                    document.getElementById('error').textContent = 'Error: ' + message.error;
                    break;
            }
        });

        // Auto-focus on prompt textarea
        document.getElementById('prompt').focus();
    </script>
</body>
</html>`;
    }
    _getContextInfo() {
        if (!this._currentContext) {
            return '';
        }
        const context = this._currentContext;
        let info = '<div class="section"><h3>Current Context</h3><div class="context-info">';
        if (context.fileName) {
            info += `<strong>File:</strong> ${context.fileName}<br>`;
        }
        if (context.language) {
            info += `<strong>Language:</strong> ${context.language}<br>`;
        }
        if (context.lineNumber) {
            info += `<strong>Line:</strong> ${context.lineNumber}<br>`;
        }
        if (context.functionContext) {
            info += `<strong>Function:</strong> ${context.functionContext}<br>`;
        }
        if (context.classContext) {
            info += `<strong>Class:</strong> ${context.classContext}<br>`;
        }
        if (context.selectedText) {
            info += `<strong>Selected Text:</strong> ${context.selectedText.length} characters<br>`;
        }
        info += '</div></div>';
        return info;
    }
}
exports.CodeGenerationPanel = CodeGenerationPanel;
//# sourceMappingURL=codeGenerationPanel.js.map