import type { FlatConfig } from '@typescript-eslint/utils/ts-eslint';
/**
 * A version of `strict` that only contains type-checked rules and disables of any corresponding core ESLint rules.
 * @see {@link https://typescript-eslint.io/users/configs#strict-type-checked-only}
 */
declare const _default: (plugin: FlatConfig.Plugin, parser: FlatConfig.Parser) => FlatConfig.ConfigArray;
export default _default;
//# sourceMappingURL=strict-type-checked-only.d.ts.map