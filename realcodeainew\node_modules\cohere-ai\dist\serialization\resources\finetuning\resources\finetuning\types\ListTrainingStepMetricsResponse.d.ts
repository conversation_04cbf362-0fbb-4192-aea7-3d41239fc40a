/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as serializers from "../../../../../index";
import * as Cohere from "../../../../../../api/index";
import * as core from "../../../../../../core";
import { TrainingStepMetrics } from "./TrainingStepMetrics";
export declare const ListTrainingStepMetricsResponse: core.serialization.ObjectSchema<serializers.finetuning.ListTrainingStepMetricsResponse.Raw, Cohere.finetuning.ListTrainingStepMetricsResponse>;
export declare namespace ListTrainingStepMetricsResponse {
    interface Raw {
        step_metrics?: TrainingStepMetrics.Raw[] | null;
        next_page_token?: string | null;
    }
}
