"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.AIService = void 0;
const vscode = __importStar(require("vscode"));
const types_1 = require("../types");
const openai_1 = require("../providers/openai");
const anthropic_1 = require("../providers/anthropic");
const google_1 = require("../providers/google");
const cohere_1 = require("../providers/cohere");
const huggingface_1 = require("../providers/huggingface");
const mistral_1 = require("../providers/mistral");
class AIService {
    providers = new Map();
    config;
    constructor() {
        this.config = this.loadConfiguration();
        this.initializeProviders();
    }
    loadConfiguration() {
        const config = vscode.workspace.getConfiguration('realCodeAI');
        return {
            defaultModel: config.get('defaultModel', 'openai-gpt3.5'),
            temperature: config.get('temperature', 0.3),
            maxTokens: config.get('maxTokens', 1000),
            includeContext: config.get('includeContext', true),
            contextLines: config.get('contextLines', 10),
            autoSave: config.get('autoSave', false),
            showPreview: config.get('showPreview', true),
            apiKeys: this.loadApiKeys()
        };
    }
    loadApiKeys() {
        const keys = {};
        const providers = ['openai', 'anthropic', 'google', 'cohere', 'huggingface', 'mistral'];
        for (const provider of providers) {
            const key = vscode.workspace.getConfiguration('realCodeAI').get(`apiKeys.${provider}`, '');
            if (key) {
                keys[provider] = key;
            }
        }
        return keys;
    }
    initializeProviders() {
        // Initialize all providers
        this.providers.set('openai', new openai_1.OpenAIProvider(this.config.apiKeys.openai));
        this.providers.set('anthropic', new anthropic_1.AnthropicProvider(this.config.apiKeys.anthropic));
        this.providers.set('google', new google_1.GoogleProvider(this.config.apiKeys.google));
        this.providers.set('cohere', new cohere_1.CohereProvider(this.config.apiKeys.cohere));
        this.providers.set('huggingface', new huggingface_1.HuggingFaceProvider(this.config.apiKeys.huggingface));
        this.providers.set('mistral', new mistral_1.MistralProvider(this.config.apiKeys.mistral));
    }
    refreshConfiguration() {
        this.config = this.loadConfiguration();
        this.updateProviderApiKeys();
    }
    updateProviderApiKeys() {
        for (const [providerName, provider] of this.providers) {
            const apiKey = this.config.apiKeys[providerName];
            if (apiKey) {
                provider.setApiKey(apiKey);
            }
        }
    }
    getSupportedModels() {
        return types_1.SUPPORTED_MODELS;
    }
    getAvailableModels() {
        return types_1.SUPPORTED_MODELS.filter(model => {
            const provider = this.providers.get(model.provider);
            return provider && this.config.apiKeys[model.provider];
        });
    }
    getProviderForModel(modelId) {
        const model = types_1.SUPPORTED_MODELS.find(m => m.id === modelId);
        if (!model) {
            throw new Error(`Unsupported model: ${modelId}`);
        }
        const provider = this.providers.get(model.provider);
        if (!provider) {
            throw new Error(`Provider not found for model: ${modelId}`);
        }
        if (!this.config.apiKeys[model.provider]) {
            throw new Error(`API key not configured for ${model.provider}. Please configure it in settings.`);
        }
        return provider;
    }
    async validateApiKey(provider, apiKey) {
        const providerInstance = this.providers.get(provider);
        if (!providerInstance) {
            return false;
        }
        try {
            return await providerInstance.validateApiKey(apiKey);
        }
        catch (error) {
            console.error(`Error validating API key for ${provider}:`, error);
            return false;
        }
    }
    async generateCode(request) {
        const modelId = this.config.defaultModel;
        const provider = this.getProviderForModel(modelId);
        // Build context if enabled
        let context = '';
        if (this.config.includeContext && request.context) {
            context = request.context;
        }
        const aiRequest = {
            prompt: request.prompt,
            model: modelId,
            temperature: this.config.temperature,
            maxTokens: this.config.maxTokens,
            context: context,
            language: request.language
        };
        try {
            const response = await provider.generateCode(aiRequest);
            return {
                generatedCode: response.content,
                model: modelId,
                explanation: this.extractExplanation(response.content),
                suggestions: this.generateSuggestions(response.content),
                confidence: this.calculateConfidence(response)
            };
        }
        catch (error) {
            throw new Error(`Code generation failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }
    async generateCodeWithModel(request, modelId) {
        const provider = this.getProviderForModel(modelId);
        let context = '';
        if (this.config.includeContext && request.context) {
            context = request.context;
        }
        const aiRequest = {
            prompt: request.prompt,
            model: modelId,
            temperature: this.config.temperature,
            maxTokens: this.config.maxTokens,
            context: context,
            language: request.language
        };
        try {
            const response = await provider.generateCode(aiRequest);
            return {
                generatedCode: response.content,
                model: modelId,
                explanation: this.extractExplanation(response.content),
                suggestions: this.generateSuggestions(response.content),
                confidence: this.calculateConfidence(response)
            };
        }
        catch (error) {
            throw new Error(`Code generation failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }
    extractExplanation(content) {
        // Look for explanation patterns in the generated content
        const explanationPatterns = [
            /\/\*\*(.*?)\*\//s,
            /\/\/(.*?)$/m,
            /"""(.*?)"""/s,
            /'''(.*?)'''/s
        ];
        for (const pattern of explanationPatterns) {
            const match = content.match(pattern);
            if (match && match[1]) {
                return match[1].trim();
            }
        }
        return undefined;
    }
    generateSuggestions(content) {
        const suggestions = [];
        // Basic suggestions based on content analysis
        if (content.includes('TODO') || content.includes('FIXME')) {
            suggestions.push('Consider implementing the TODO/FIXME items');
        }
        if (content.includes('console.log') || content.includes('print(')) {
            suggestions.push('Remove debug statements before production');
        }
        if (content.length < 50) {
            suggestions.push('Consider adding more detailed implementation');
        }
        return suggestions;
    }
    calculateConfidence(response) {
        // Simple confidence calculation based on response characteristics
        let confidence = 0.8; // Base confidence
        if (response.finishReason === 'stop') {
            confidence += 0.1;
        }
        if (response.usage && response.usage.totalTokens > 0) {
            // Higher token usage might indicate more detailed response
            const tokenRatio = response.usage.completionTokens / response.usage.totalTokens;
            confidence += tokenRatio * 0.1;
        }
        return Math.min(confidence, 1.0);
    }
}
exports.AIService = AIService;
//# sourceMappingURL=aiService.js.map