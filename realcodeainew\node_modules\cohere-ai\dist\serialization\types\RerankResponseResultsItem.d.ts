/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as serializers from "../index";
import * as Cohere from "../../api/index";
import * as core from "../../core";
import { RerankResponseResultsItemDocument } from "./RerankResponseResultsItemDocument";
export declare const RerankResponseResultsItem: core.serialization.ObjectSchema<serializers.RerankResponseResultsItem.Raw, Cohere.RerankResponseResultsItem>;
export declare namespace RerankResponseResultsItem {
    interface Raw {
        document?: RerankResponseResultsItemDocument.Raw | null;
        index: number;
        relevance_score: number;
    }
}
