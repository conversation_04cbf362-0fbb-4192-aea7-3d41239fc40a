/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as serializers from "../index";
import * as Cohere from "../../api/index";
import * as core from "../../core";
export declare const OAuthAuthorizeResponse: core.serialization.ObjectSchema<serializers.OAuthAuthorizeResponse.Raw, Cohere.OAuthAuthorizeResponse>;
export declare namespace OAuthAuthorizeResponse {
    interface Raw {
        redirect_url?: string | null;
    }
}
