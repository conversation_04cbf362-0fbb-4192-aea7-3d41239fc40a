/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as serializers from "../../../index";
import * as Cohere from "../../../../api/index";
import * as core from "../../../../core";
import { Document } from "../../../types/Document";
export declare const V2ChatRequestDocumentsItem: core.serialization.Schema<serializers.V2ChatRequestDocumentsItem.Raw, Cohere.V2ChatRequestDocumentsItem>;
export declare namespace V2ChatRequestDocumentsItem {
    type Raw = string | Document.Raw;
}
