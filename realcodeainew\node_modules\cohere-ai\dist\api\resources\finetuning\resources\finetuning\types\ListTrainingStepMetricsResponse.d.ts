/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as Cohere from "../../../../../index";
/**
 * Response to a request to list training-step metrics of a fine-tuned model.
 */
export interface ListTrainingStepMetricsResponse {
    /** The metrics for each step the evaluation was run on. */
    stepMetrics?: Cohere.finetuning.TrainingStepMetrics[];
    /**
     * Pagination token to retrieve the next page of results. If the value is "",
     * it means no further results for the request.
     */
    nextPageToken?: string;
}
