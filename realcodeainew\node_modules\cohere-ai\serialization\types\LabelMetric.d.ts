/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as serializers from "../index";
import * as Cohere from "../../api/index";
import * as core from "../../core";
export declare const LabelMetric: core.serialization.ObjectSchema<serializers.LabelMetric.Raw, Cohere.LabelMetric>;
export declare namespace LabelMetric {
    interface Raw {
        total_examples?: number | null;
        label?: string | null;
        samples?: string[] | null;
    }
}
