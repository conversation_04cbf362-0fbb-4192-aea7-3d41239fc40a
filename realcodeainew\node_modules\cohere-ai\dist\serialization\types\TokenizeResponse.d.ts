/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as serializers from "../index";
import * as Cohere from "../../api/index";
import * as core from "../../core";
import { ApiMeta } from "./ApiMeta";
export declare const TokenizeResponse: core.serialization.ObjectSchema<serializers.TokenizeResponse.Raw, Cohere.TokenizeResponse>;
export declare namespace TokenizeResponse {
    interface Raw {
        tokens: number[];
        token_strings: string[];
        meta?: ApiMeta.Raw | null;
    }
}
