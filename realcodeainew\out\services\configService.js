"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.ConfigService = void 0;
const vscode = __importStar(require("vscode"));
const types_1 = require("../types");
class ConfigService {
    context;
    static CONFIG_SECTION = 'realCodeAI';
    static SECRET_STORAGE_PREFIX = 'realCodeAI.apiKey.';
    constructor(context) {
        this.context = context;
    }
    async getConfiguration() {
        const config = vscode.workspace.getConfiguration(ConfigService.CONFIG_SECTION);
        const apiKeys = await this.getApiKeys();
        return {
            defaultModel: config.get('defaultModel', 'openai-gpt3.5'),
            temperature: config.get('temperature', 0.3),
            maxTokens: config.get('maxTokens', 1000),
            includeContext: config.get('includeContext', true),
            contextLines: config.get('contextLines', 10),
            autoSave: config.get('autoSave', false),
            showPreview: config.get('showPreview', true),
            apiKeys: apiKeys
        };
    }
    async setApiKey(provider, apiKey) {
        const key = `${ConfigService.SECRET_STORAGE_PREFIX}${provider}`;
        if (apiKey && apiKey.trim()) {
            await this.context.secrets.store(key, apiKey.trim());
        }
        else {
            await this.context.secrets.delete(key);
        }
    }
    async getApiKey(provider) {
        const key = `${ConfigService.SECRET_STORAGE_PREFIX}${provider}`;
        return await this.context.secrets.get(key);
    }
    async getApiKeys() {
        const keys = {};
        const providers = ['openai', 'anthropic', 'google', 'cohere', 'huggingface', 'mistral'];
        for (const provider of providers) {
            const apiKey = await this.getApiKey(provider);
            if (apiKey) {
                keys[provider] = apiKey;
            }
        }
        return keys;
    }
    async deleteApiKey(provider) {
        const key = `${ConfigService.SECRET_STORAGE_PREFIX}${provider}`;
        await this.context.secrets.delete(key);
    }
    async deleteAllApiKeys() {
        const providers = ['openai', 'anthropic', 'google', 'cohere', 'huggingface', 'mistral'];
        for (const provider of providers) {
            await this.deleteApiKey(provider);
        }
    }
    async updateConfiguration(updates) {
        const config = vscode.workspace.getConfiguration(ConfigService.CONFIG_SECTION);
        if (updates.defaultModel !== undefined) {
            await config.update('defaultModel', updates.defaultModel, vscode.ConfigurationTarget.Global);
        }
        if (updates.temperature !== undefined) {
            await config.update('temperature', updates.temperature, vscode.ConfigurationTarget.Global);
        }
        if (updates.maxTokens !== undefined) {
            await config.update('maxTokens', updates.maxTokens, vscode.ConfigurationTarget.Global);
        }
        if (updates.includeContext !== undefined) {
            await config.update('includeContext', updates.includeContext, vscode.ConfigurationTarget.Global);
        }
        if (updates.contextLines !== undefined) {
            await config.update('contextLines', updates.contextLines, vscode.ConfigurationTarget.Global);
        }
        if (updates.autoSave !== undefined) {
            await config.update('autoSave', updates.autoSave, vscode.ConfigurationTarget.Global);
        }
        if (updates.showPreview !== undefined) {
            await config.update('showPreview', updates.showPreview, vscode.ConfigurationTarget.Global);
        }
        // Handle API keys separately using secret storage
        if (updates.apiKeys) {
            for (const [provider, apiKey] of Object.entries(updates.apiKeys)) {
                await this.setApiKey(provider, apiKey);
            }
        }
    }
    getAvailableModels() {
        return types_1.SUPPORTED_MODELS;
    }
    async getConfiguredModels() {
        const apiKeys = await this.getApiKeys();
        return types_1.SUPPORTED_MODELS.filter(model => apiKeys[model.provider]);
    }
    validateConfiguration(config) {
        const errors = [];
        if (config.temperature !== undefined) {
            if (config.temperature < 0 || config.temperature > 2) {
                errors.push('Temperature must be between 0 and 2');
            }
        }
        if (config.maxTokens !== undefined) {
            if (config.maxTokens < 50 || config.maxTokens > 4000) {
                errors.push('Max tokens must be between 50 and 4000');
            }
        }
        if (config.contextLines !== undefined) {
            if (config.contextLines < 0 || config.contextLines > 50) {
                errors.push('Context lines must be between 0 and 50');
            }
        }
        if (config.defaultModel !== undefined) {
            const validModels = types_1.SUPPORTED_MODELS.map(m => m.id);
            if (!validModels.includes(config.defaultModel)) {
                errors.push(`Invalid default model: ${config.defaultModel}`);
            }
        }
        return errors;
    }
    async resetToDefaults() {
        const config = vscode.workspace.getConfiguration(ConfigService.CONFIG_SECTION);
        await config.update('defaultModel', 'openai-gpt3.5', vscode.ConfigurationTarget.Global);
        await config.update('temperature', 0.3, vscode.ConfigurationTarget.Global);
        await config.update('maxTokens', 1000, vscode.ConfigurationTarget.Global);
        await config.update('includeContext', true, vscode.ConfigurationTarget.Global);
        await config.update('contextLines', 10, vscode.ConfigurationTarget.Global);
        await config.update('autoSave', false, vscode.ConfigurationTarget.Global);
        await config.update('showPreview', true, vscode.ConfigurationTarget.Global);
    }
    async exportConfiguration() {
        const config = await this.getConfiguration();
        // Don't export API keys for security
        const exportConfig = { ...config };
        delete exportConfig.apiKeys;
        return JSON.stringify(exportConfig, null, 2);
    }
    async importConfiguration(configJson) {
        try {
            const config = JSON.parse(configJson);
            const errors = this.validateConfiguration(config);
            if (errors.length > 0) {
                throw new Error(`Invalid configuration: ${errors.join(', ')}`);
            }
            await this.updateConfiguration(config);
        }
        catch (error) {
            throw new Error(`Failed to import configuration: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }
}
exports.ConfigService = ConfigService;
//# sourceMappingURL=configService.js.map