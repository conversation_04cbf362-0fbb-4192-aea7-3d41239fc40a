/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as serializers from "../index";
import * as Cohere from "../../api/index";
import * as core from "../../core";
export declare const ToolV2Function: core.serialization.ObjectSchema<serializers.ToolV2Function.Raw, Cohere.ToolV2Function>;
export declare namespace ToolV2Function {
    interface Raw {
        name: string;
        description?: string | null;
        parameters: Record<string, unknown>;
    }
}
