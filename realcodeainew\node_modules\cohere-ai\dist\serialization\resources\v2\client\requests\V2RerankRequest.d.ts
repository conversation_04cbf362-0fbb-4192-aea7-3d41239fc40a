/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as serializers from "../../../../index";
import * as Cohere from "../../../../../api/index";
import * as core from "../../../../../core";
export declare const V2RerankRequest: core.serialization.Schema<serializers.V2RerankRequest.Raw, Cohere.V2RerankRequest>;
export declare namespace V2RerankRequest {
    interface Raw {
        model: string;
        query: string;
        documents: string[];
        top_n?: number | null;
        max_tokens_per_doc?: number | null;
    }
}
