"use strict";
// Type definitions for Real Code AI extension
Object.defineProperty(exports, "__esModule", { value: true });
exports.SUPPORTED_MODELS = exports.AIProviderType = void 0;
var AIProviderType;
(function (AIProviderType) {
    AIProviderType["OPENAI"] = "openai";
    AIProviderType["ANTHROPIC"] = "anthropic";
    AIProviderType["GOOGLE"] = "google";
    AIProviderType["COHERE"] = "cohere";
    AIProviderType["HUGGINGFACE"] = "huggingface";
    AIProviderType["MISTRAL"] = "mistral";
})(AIProviderType || (exports.AIProviderType = AIProviderType = {}));
exports.SUPPORTED_MODELS = [
    {
        id: 'openai-gpt4',
        name: 'GPT-4',
        description: 'Most capable model, excellent for complex code generation',
        provider: 'openai',
        maxTokens: 8192,
        supportsStreaming: true
    },
    {
        id: 'openai-gpt3.5',
        name: 'GPT-3.5 Turbo',
        description: 'Fast and efficient, good for most coding tasks',
        provider: 'openai',
        maxTokens: 4096,
        supportsStreaming: true
    },
    {
        id: 'anthropic-claude',
        name: 'Claude 3',
        description: 'Excellent for code analysis and complex reasoning',
        provider: 'anthropic',
        maxTokens: 4096,
        supportsStreaming: true
    },
    {
        id: 'google-gemini',
        name: 'Gemini Pro',
        description: 'Google\'s advanced model for code and reasoning',
        provider: 'google',
        maxTokens: 2048,
        supportsStreaming: false
    },
    {
        id: 'openai-codex',
        name: 'Codex',
        description: 'Specialized for code generation and completion',
        provider: 'openai',
        maxTokens: 4096,
        supportsStreaming: false
    },
    {
        id: 'cohere-command',
        name: 'Command',
        description: 'Cohere\'s model for text and code generation',
        provider: 'cohere',
        maxTokens: 2048,
        supportsStreaming: true
    },
    {
        id: 'huggingface-codegen',
        name: 'CodeGen',
        description: 'Open source code generation model',
        provider: 'huggingface',
        maxTokens: 2048,
        supportsStreaming: false
    },
    {
        id: 'mistral-codestral',
        name: 'Codestral',
        description: 'Mistral\'s specialized code model',
        provider: 'mistral',
        maxTokens: 2048,
        supportsStreaming: true
    },
    {
        id: 'google-palm',
        name: 'PaLM 2',
        description: 'Google\'s foundation model for various tasks',
        provider: 'google',
        maxTokens: 1024,
        supportsStreaming: false
    }
];
//# sourceMappingURL=types.js.map