/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as serializers from "../index";
import * as Cohere from "../../api/index";
import * as core from "../../core";
import { Connector } from "./Connector";
export declare const UpdateConnectorResponse: core.serialization.ObjectSchema<serializers.UpdateConnectorResponse.Raw, Cohere.UpdateConnectorResponse>;
export declare namespace UpdateConnectorResponse {
    interface Raw {
        connector: Connector.Raw;
    }
}
