/**
 * This file was auto-generated by Fern from our API Definition.
 */
export interface RerankerDataMetrics {
    /** The number of training queries. */
    numTrainQueries?: number;
    /** The sum of all relevant passages of valid training examples. */
    numTrainRelevantPassages?: number;
    /** The sum of all hard negatives of valid training examples. */
    numTrainHardNegatives?: number;
    /** The number of evaluation queries. */
    numEvalQueries?: number;
    /** The sum of all relevant passages of valid eval examples. */
    numEvalRelevantPassages?: number;
    /** The sum of all hard negatives of valid eval examples. */
    numEvalHardNegatives?: number;
}
