"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.CodeGenerationCommands = void 0;
const vscode = __importStar(require("vscode"));
const contextService_1 = require("../services/contextService");
const codeGenerationPanel_1 = require("../webview/codeGenerationPanel");
const settingsPanel_1 = require("../webview/settingsPanel");
class CodeGenerationCommands {
    context;
    aiService;
    configService;
    constructor(context, aiService, configService) {
        this.context = context;
        this.aiService = aiService;
        this.configService = configService;
    }
    registerCommands() {
        // Register all commands
        this.context.subscriptions.push(vscode.commands.registerCommand('realCodeAI.generateCode', () => this.generateCode()), vscode.commands.registerCommand('realCodeAI.generateCodeInline', () => this.generateCodeInline()), vscode.commands.registerCommand('realCodeAI.openSettings', () => this.openSettings()), vscode.commands.registerCommand('realCodeAI.explainCode', () => this.explainCode()), vscode.commands.registerCommand('realCodeAI.refactorCode', () => this.refactorCode()), vscode.commands.registerCommand('realCodeAI.generateTests', () => this.generateTests()));
    }
    async generateCode() {
        const editor = vscode.window.activeTextEditor;
        if (!editor) {
            vscode.window.showWarningMessage('Please open a file to generate code.');
            return;
        }
        try {
            // Check if any models are configured
            const availableModels = this.aiService.getAvailableModels();
            if (availableModels.length === 0) {
                const result = await vscode.window.showWarningMessage('No AI models are configured. Please configure API keys first.', 'Open Settings');
                if (result === 'Open Settings') {
                    this.openSettings();
                }
                return;
            }
            // Extract context
            const config = await this.configService.getConfiguration();
            const context = contextService_1.ContextService.extractContext(editor, config.contextLines);
            // Show code generation panel
            codeGenerationPanel_1.CodeGenerationPanel.createOrShow(this.context.extensionUri, this.aiService, context);
        }
        catch (error) {
            vscode.window.showErrorMessage(`Failed to open code generation panel: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }
    async generateCodeInline() {
        const editor = vscode.window.activeTextEditor;
        if (!editor) {
            vscode.window.showWarningMessage('Please open a file to generate code.');
            return;
        }
        try {
            // Check if any models are configured
            const availableModels = this.aiService.getAvailableModels();
            if (availableModels.length === 0) {
                const result = await vscode.window.showWarningMessage('No AI models are configured. Please configure API keys first.', 'Open Settings');
                if (result === 'Open Settings') {
                    this.openSettings();
                }
                return;
            }
            // Get prompt from user
            const prompt = await vscode.window.showInputBox({
                prompt: 'Describe the code you want to generate',
                placeHolder: 'e.g., Create a function that sorts an array of objects by a specific property'
            });
            if (!prompt) {
                return;
            }
            // Show progress
            await vscode.window.withProgress({
                location: vscode.ProgressLocation.Notification,
                title: 'Generating code...',
                cancellable: false
            }, async (progress) => {
                try {
                    // Extract context
                    const config = await this.configService.getConfiguration();
                    const context = contextService_1.ContextService.extractContext(editor, config.contextLines);
                    // Generate code
                    const request = {
                        prompt: prompt,
                        selectedText: context.selectedText,
                        fileName: context.fileName,
                        language: context.language,
                        context: contextService_1.ContextService.buildContextPrompt(context)
                    };
                    const result = await this.aiService.generateCode(request);
                    // Show preview if enabled
                    if (config.showPreview) {
                        const action = await vscode.window.showInformationMessage('Code generated successfully. What would you like to do?', 'Insert at Cursor', 'Replace Selection', 'Copy to Clipboard', 'Cancel');
                        switch (action) {
                            case 'Insert at Cursor':
                                await contextService_1.ContextService.insertCodeAtPosition(editor, result.generatedCode);
                                break;
                            case 'Replace Selection':
                                await contextService_1.ContextService.replaceSelectedText(editor, result.generatedCode);
                                break;
                            case 'Copy to Clipboard':
                                await vscode.env.clipboard.writeText(result.generatedCode);
                                vscode.window.showInformationMessage('Code copied to clipboard.');
                                break;
                        }
                    }
                    else {
                        // Insert directly
                        await contextService_1.ContextService.insertCodeAtPosition(editor, result.generatedCode);
                        vscode.window.showInformationMessage('Code generated and inserted.');
                    }
                    // Auto-save if enabled
                    if (config.autoSave) {
                        await editor.document.save();
                    }
                }
                catch (error) {
                    vscode.window.showErrorMessage(`Code generation failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
                }
            });
        }
        catch (error) {
            vscode.window.showErrorMessage(`Failed to generate code: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }
    async explainCode() {
        const editor = vscode.window.activeTextEditor;
        if (!editor) {
            vscode.window.showWarningMessage('Please open a file and select code to explain.');
            return;
        }
        const selection = editor.selection;
        if (selection.isEmpty) {
            vscode.window.showWarningMessage('Please select some code to explain.');
            return;
        }
        try {
            const availableModels = this.aiService.getAvailableModels();
            if (availableModels.length === 0) {
                const result = await vscode.window.showWarningMessage('No AI models are configured. Please configure API keys first.', 'Open Settings');
                if (result === 'Open Settings') {
                    this.openSettings();
                }
                return;
            }
            const selectedText = editor.document.getText(selection);
            const context = contextService_1.ContextService.extractContext(editor, 5);
            await vscode.window.withProgress({
                location: vscode.ProgressLocation.Notification,
                title: 'Explaining code...',
                cancellable: false
            }, async () => {
                try {
                    const request = {
                        prompt: `Please explain what this code does:\n\n${selectedText}`,
                        selectedText: selectedText,
                        fileName: context.fileName,
                        language: context.language,
                        context: contextService_1.ContextService.buildContextPrompt(context)
                    };
                    const result = await this.aiService.generateCode(request);
                    // Show explanation in a new document
                    const doc = await vscode.workspace.openTextDocument({
                        content: `Code Explanation\n================\n\nOriginal Code:\n${selectedText}\n\nExplanation:\n${result.generatedCode}`,
                        language: 'markdown'
                    });
                    await vscode.window.showTextDocument(doc);
                }
                catch (error) {
                    vscode.window.showErrorMessage(`Code explanation failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
                }
            });
        }
        catch (error) {
            vscode.window.showErrorMessage(`Failed to explain code: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }
    async refactorCode() {
        const editor = vscode.window.activeTextEditor;
        if (!editor) {
            vscode.window.showWarningMessage('Please open a file and select code to refactor.');
            return;
        }
        const selection = editor.selection;
        if (selection.isEmpty) {
            vscode.window.showWarningMessage('Please select some code to refactor.');
            return;
        }
        try {
            const availableModels = this.aiService.getAvailableModels();
            if (availableModels.length === 0) {
                const result = await vscode.window.showWarningMessage('No AI models are configured. Please configure API keys first.', 'Open Settings');
                if (result === 'Open Settings') {
                    this.openSettings();
                }
                return;
            }
            const refactorType = await vscode.window.showQuickPick([
                'Improve readability',
                'Optimize performance',
                'Add error handling',
                'Add comments',
                'Extract functions',
                'Simplify logic',
                'Custom refactoring'
            ], {
                placeHolder: 'Select refactoring type'
            });
            if (!refactorType) {
                return;
            }
            let prompt = '';
            if (refactorType === 'Custom refactoring') {
                const customPrompt = await vscode.window.showInputBox({
                    prompt: 'Describe how you want to refactor the code',
                    placeHolder: 'e.g., Convert to async/await, use modern ES6 syntax'
                });
                if (!customPrompt) {
                    return;
                }
                prompt = `Refactor this code: ${customPrompt}`;
            }
            else {
                prompt = `Refactor this code to ${refactorType.toLowerCase()}`;
            }
            const selectedText = editor.document.getText(selection);
            const context = contextService_1.ContextService.extractContext(editor, 10);
            await vscode.window.withProgress({
                location: vscode.ProgressLocation.Notification,
                title: 'Refactoring code...',
                cancellable: false
            }, async () => {
                try {
                    const request = {
                        prompt: `${prompt}:\n\n${selectedText}`,
                        selectedText: selectedText,
                        fileName: context.fileName,
                        language: context.language,
                        context: contextService_1.ContextService.buildContextPrompt(context)
                    };
                    const result = await this.aiService.generateCode(request);
                    // Show refactored code in code generation panel
                    codeGenerationPanel_1.CodeGenerationPanel.createOrShow(this.context.extensionUri, this.aiService, context, `${prompt}:\n\n${selectedText}`);
                }
                catch (error) {
                    vscode.window.showErrorMessage(`Code refactoring failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
                }
            });
        }
        catch (error) {
            vscode.window.showErrorMessage(`Failed to refactor code: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }
    async generateTests() {
        const editor = vscode.window.activeTextEditor;
        if (!editor) {
            vscode.window.showWarningMessage('Please open a file to generate tests for.');
            return;
        }
        try {
            const availableModels = this.aiService.getAvailableModels();
            if (availableModels.length === 0) {
                const result = await vscode.window.showWarningMessage('No AI models are configured. Please configure API keys first.', 'Open Settings');
                if (result === 'Open Settings') {
                    this.openSettings();
                }
                return;
            }
            const selection = editor.selection;
            const context = contextService_1.ContextService.extractContext(editor, 20);
            let codeToTest = '';
            let prompt = '';
            if (!selection.isEmpty) {
                codeToTest = editor.document.getText(selection);
                prompt = `Generate comprehensive unit tests for this code:\n\n${codeToTest}`;
            }
            else {
                // Use the entire file or function context
                codeToTest = context.surroundingCode || '';
                prompt = `Generate comprehensive unit tests for this ${context.language} code. Focus on testing the main functions and edge cases.`;
            }
            const testFramework = await vscode.window.showQuickPick([
                'Jest (JavaScript/TypeScript)',
                'Mocha (JavaScript/TypeScript)',
                'pytest (Python)',
                'JUnit (Java)',
                'NUnit (C#)',
                'Google Test (C++)',
                'Auto-detect framework'
            ], {
                placeHolder: 'Select testing framework'
            });
            if (!testFramework) {
                return;
            }
            if (testFramework !== 'Auto-detect framework') {
                prompt += `\n\nUse ${testFramework} testing framework.`;
            }
            // Show test generation in panel
            codeGenerationPanel_1.CodeGenerationPanel.createOrShow(this.context.extensionUri, this.aiService, context, prompt);
        }
        catch (error) {
            vscode.window.showErrorMessage(`Failed to generate tests: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }
    openSettings() {
        settingsPanel_1.SettingsPanel.createOrShow(this.context.extensionUri, this.configService, this.aiService);
    }
}
exports.CodeGenerationCommands = CodeGenerationCommands;
//# sourceMappingURL=codeGenerationCommands.js.map