"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AnthropicProvider = void 0;
const sdk_1 = __importDefault(require("@anthropic-ai/sdk"));
const base_1 = require("./base");
class AnthropicProvider extends base_1.BaseAIProvider {
    name = 'Anthropic';
    models = [
        {
            id: 'anthropic-claude',
            name: 'Claude 3',
            description: 'Excellent for code analysis and complex reasoning',
            provider: 'anthropic',
            maxTokens: 4096,
            supportsStreaming: true
        }
    ];
    client = null;
    constructor(apiKey) {
        super(apiKey);
        if (apiKey) {
            this.initializeClient(apiKey);
        }
    }
    initializeClient(apiKey) {
        this.client = new sdk_1.default({
            apiKey: apiKey,
        });
    }
    setApiKey(apiKey) {
        super.setApiKey(apiKey);
        this.initializeClient(apiKey);
    }
    async validateApiKey(apiKey) {
        try {
            const tempClient = new sdk_1.default({ apiKey });
            // Try a minimal request to validate the key
            await tempClient.completions.create({
                model: 'claude-instant-1',
                max_tokens_to_sample: 10,
                prompt: '\n\nHuman: Hi\n\nAssistant:'
            });
            return true;
        }
        catch (error) {
            return false;
        }
    }
    async generateCode(request) {
        if (!this.client) {
            throw new Error('Anthropic client not initialized. Please set API key first.');
        }
        try {
            const prompt = this.buildPrompt(request);
            const fullPrompt = `\n\nHuman: You are an expert programmer. Generate clean, efficient, and well-commented code based on the following requirements:\n\n${prompt}\n\nAssistant:`;
            const response = await this.client.completions.create({
                model: 'claude-instant-1',
                max_tokens_to_sample: request.maxTokens || 1000,
                temperature: request.temperature || 0.3,
                prompt: fullPrompt
            });
            return {
                content: response.completion.trim(),
                model: request.model,
                usage: {
                    promptTokens: 0, // Anthropic doesn't provide detailed token usage in legacy API
                    completionTokens: 0,
                    totalTokens: 0
                },
                finishReason: response.stop_reason || 'unknown'
            };
        }
        catch (error) {
            this.handleError(error);
        }
    }
}
exports.AnthropicProvider = AnthropicProvider;
//# sourceMappingURL=anthropic.js.map