/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as serializers from "../index";
import * as Cohere from "../../api/index";
import * as core from "../../core";
export declare const ToolCallV2Function: core.serialization.ObjectSchema<serializers.ToolCallV2Function.Raw, Cohere.ToolCallV2Function>;
export declare namespace ToolCallV2Function {
    interface Raw {
        name?: string | null;
        arguments?: string | null;
    }
}
