/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as serializers from "../index";
import * as Cohere from "../../api/index";
import * as core from "../../core";
export declare const ToolParameterDefinitionsValue: core.serialization.ObjectSchema<serializers.ToolParameterDefinitionsValue.Raw, Cohere.ToolParameterDefinitionsValue>;
export declare namespace ToolParameterDefinitionsValue {
    interface Raw {
        description?: string | null;
        type: string;
        required?: boolean | null;
    }
}
