/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as serializers from "../index";
import * as Cohere from "../../api/index";
import * as core from "../../core";
export declare const JsonResponseFormatV2: core.serialization.ObjectSchema<serializers.JsonResponseFormatV2.Raw, Cohere.JsonResponseFormatV2>;
export declare namespace JsonResponseFormatV2 {
    interface Raw {
        json_schema?: Record<string, unknown> | null;
    }
}
