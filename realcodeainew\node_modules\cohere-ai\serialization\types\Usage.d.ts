/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as serializers from "../index";
import * as Cohere from "../../api/index";
import * as core from "../../core";
import { UsageBilledUnits } from "./UsageBilledUnits";
import { UsageTokens } from "./UsageTokens";
export declare const Usage: core.serialization.ObjectSchema<serializers.Usage.Raw, Cohere.Usage>;
export declare namespace Usage {
    interface Raw {
        billed_units?: UsageBilledUnits.Raw | null;
        tokens?: UsageTokens.Raw | null;
    }
}
