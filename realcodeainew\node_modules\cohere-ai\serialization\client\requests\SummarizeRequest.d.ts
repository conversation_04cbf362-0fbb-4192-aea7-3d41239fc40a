/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as serializers from "../../index";
import * as Cohere from "../../../api/index";
import * as core from "../../../core";
import { SummarizeRequestLength } from "../../types/SummarizeRequestLength";
import { SummarizeRequestFormat } from "../../types/SummarizeRequestFormat";
import { SummarizeRequestExtractiveness } from "../../types/SummarizeRequestExtractiveness";
export declare const SummarizeRequest: core.serialization.Schema<serializers.SummarizeRequest.Raw, Cohere.SummarizeRequest>;
export declare namespace SummarizeRequest {
    interface Raw {
        text: string;
        length?: SummarizeRequestLength.Raw | null;
        format?: SummarizeRequestFormat.Raw | null;
        model?: string | null;
        extractiveness?: SummarizeRequestExtractiveness.Raw | null;
        temperature?: number | null;
        additional_command?: string | null;
    }
}
