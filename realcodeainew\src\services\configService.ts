import * as vscode from 'vscode';
import { ExtensionConfig, AIModel, SUPPORTED_MODELS } from '../types';

export class ConfigService {
  private static readonly CONFIG_SECTION = 'realCodeAI';
  private static readonly SECRET_STORAGE_PREFIX = 'realCodeAI.apiKey.';

  constructor(private context: vscode.ExtensionContext) {}

  public async getConfiguration(): Promise<ExtensionConfig> {
    const config = vscode.workspace.getConfiguration(ConfigService.CONFIG_SECTION);
    const apiKeys = await this.getApiKeys();

    return {
      defaultModel: config.get('defaultModel', 'openai-gpt3.5'),
      temperature: config.get('temperature', 0.3),
      maxTokens: config.get('maxTokens', 1000),
      includeContext: config.get('includeContext', true),
      contextLines: config.get('contextLines', 10),
      autoSave: config.get('autoSave', false),
      showPreview: config.get('showPreview', true),
      apiKeys: apiKeys
    };
  }

  public async setApiKey(provider: string, apiKey: string): Promise<void> {
    const key = `${ConfigService.SECRET_STORAGE_PREFIX}${provider}`;
    if (apiKey && apiKey.trim()) {
      await this.context.secrets.store(key, apiKey.trim());
    } else {
      await this.context.secrets.delete(key);
    }
  }

  public async getApiKey(provider: string): Promise<string | undefined> {
    const key = `${ConfigService.SECRET_STORAGE_PREFIX}${provider}`;
    return await this.context.secrets.get(key);
  }

  public async getApiKeys(): Promise<{ [provider: string]: string }> {
    const keys: { [provider: string]: string } = {};
    const providers = ['openai', 'anthropic', 'google', 'cohere', 'huggingface', 'mistral'];
    
    for (const provider of providers) {
      const apiKey = await this.getApiKey(provider);
      if (apiKey) {
        keys[provider] = apiKey;
      }
    }
    
    return keys;
  }

  public async deleteApiKey(provider: string): Promise<void> {
    const key = `${ConfigService.SECRET_STORAGE_PREFIX}${provider}`;
    await this.context.secrets.delete(key);
  }

  public async deleteAllApiKeys(): Promise<void> {
    const providers = ['openai', 'anthropic', 'google', 'cohere', 'huggingface', 'mistral'];
    for (const provider of providers) {
      await this.deleteApiKey(provider);
    }
  }

  public async updateConfiguration(updates: Partial<ExtensionConfig>): Promise<void> {
    const config = vscode.workspace.getConfiguration(ConfigService.CONFIG_SECTION);
    
    if (updates.defaultModel !== undefined) {
      await config.update('defaultModel', updates.defaultModel, vscode.ConfigurationTarget.Global);
    }
    
    if (updates.temperature !== undefined) {
      await config.update('temperature', updates.temperature, vscode.ConfigurationTarget.Global);
    }
    
    if (updates.maxTokens !== undefined) {
      await config.update('maxTokens', updates.maxTokens, vscode.ConfigurationTarget.Global);
    }
    
    if (updates.includeContext !== undefined) {
      await config.update('includeContext', updates.includeContext, vscode.ConfigurationTarget.Global);
    }
    
    if (updates.contextLines !== undefined) {
      await config.update('contextLines', updates.contextLines, vscode.ConfigurationTarget.Global);
    }
    
    if (updates.autoSave !== undefined) {
      await config.update('autoSave', updates.autoSave, vscode.ConfigurationTarget.Global);
    }
    
    if (updates.showPreview !== undefined) {
      await config.update('showPreview', updates.showPreview, vscode.ConfigurationTarget.Global);
    }

    // Handle API keys separately using secret storage
    if (updates.apiKeys) {
      for (const [provider, apiKey] of Object.entries(updates.apiKeys)) {
        await this.setApiKey(provider, apiKey);
      }
    }
  }

  public getAvailableModels(): AIModel[] {
    return SUPPORTED_MODELS;
  }

  public async getConfiguredModels(): Promise<AIModel[]> {
    const apiKeys = await this.getApiKeys();
    return SUPPORTED_MODELS.filter(model => apiKeys[model.provider]);
  }

  public validateConfiguration(config: Partial<ExtensionConfig>): string[] {
    const errors: string[] = [];

    if (config.temperature !== undefined) {
      if (config.temperature < 0 || config.temperature > 2) {
        errors.push('Temperature must be between 0 and 2');
      }
    }

    if (config.maxTokens !== undefined) {
      if (config.maxTokens < 50 || config.maxTokens > 4000) {
        errors.push('Max tokens must be between 50 and 4000');
      }
    }

    if (config.contextLines !== undefined) {
      if (config.contextLines < 0 || config.contextLines > 50) {
        errors.push('Context lines must be between 0 and 50');
      }
    }

    if (config.defaultModel !== undefined) {
      const validModels = SUPPORTED_MODELS.map(m => m.id);
      if (!validModels.includes(config.defaultModel)) {
        errors.push(`Invalid default model: ${config.defaultModel}`);
      }
    }

    return errors;
  }

  public async resetToDefaults(): Promise<void> {
    const config = vscode.workspace.getConfiguration(ConfigService.CONFIG_SECTION);
    
    await config.update('defaultModel', 'openai-gpt3.5', vscode.ConfigurationTarget.Global);
    await config.update('temperature', 0.3, vscode.ConfigurationTarget.Global);
    await config.update('maxTokens', 1000, vscode.ConfigurationTarget.Global);
    await config.update('includeContext', true, vscode.ConfigurationTarget.Global);
    await config.update('contextLines', 10, vscode.ConfigurationTarget.Global);
    await config.update('autoSave', false, vscode.ConfigurationTarget.Global);
    await config.update('showPreview', true, vscode.ConfigurationTarget.Global);
  }

  public async exportConfiguration(): Promise<string> {
    const config = await this.getConfiguration();
    // Don't export API keys for security
    const exportConfig: Partial<ExtensionConfig> = { ...config };
    delete exportConfig.apiKeys;

    return JSON.stringify(exportConfig, null, 2);
  }

  public async importConfiguration(configJson: string): Promise<void> {
    try {
      const config = JSON.parse(configJson) as Partial<ExtensionConfig>;
      const errors = this.validateConfiguration(config);
      
      if (errors.length > 0) {
        throw new Error(`Invalid configuration: ${errors.join(', ')}`);
      }
      
      await this.updateConfiguration(config);
    } catch (error) {
      throw new Error(`Failed to import configuration: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }
}
