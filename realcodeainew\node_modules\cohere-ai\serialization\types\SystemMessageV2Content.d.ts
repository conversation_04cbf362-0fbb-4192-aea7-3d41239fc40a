/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as serializers from "../index";
import * as Cohere from "../../api/index";
import * as core from "../../core";
import { SystemMessageV2ContentItem } from "./SystemMessageV2ContentItem";
export declare const SystemMessageV2Content: core.serialization.Schema<serializers.SystemMessageV2Content.Raw, Cohere.SystemMessageV2Content>;
export declare namespace SystemMessageV2Content {
    type Raw = string | SystemMessageV2ContentItem.Raw[];
}
