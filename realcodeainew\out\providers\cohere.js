"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.CohereProvider = void 0;
const cohere_ai_1 = require("cohere-ai");
const base_1 = require("./base");
class CohereProvider extends base_1.BaseAIProvider {
    name = 'Cohere';
    models = [
        {
            id: 'cohere-command',
            name: 'Command',
            description: 'Cohere\'s model for text and code generation',
            provider: 'cohere',
            maxTokens: 2048,
            supportsStreaming: true
        }
    ];
    client = null;
    constructor(apiKey) {
        super(apiKey);
        if (apiKey) {
            this.initializeClient(apiKey);
        }
    }
    initializeClient(apiKey) {
        this.client = new cohere_ai_1.CohereClient({
            token: apiKey,
        });
    }
    setApiKey(apiKey) {
        super.setApiKey(apiKey);
        this.initializeClient(apiKey);
    }
    async validateApiKey(apiKey) {
        try {
            const tempClient = new cohere_ai_1.CohereClient({ token: api<PERSON><PERSON> });
            await tempClient.generate({
                model: 'command',
                prompt: 'Hi',
                maxTokens: 5
            });
            return true;
        }
        catch (error) {
            return false;
        }
    }
    async generateCode(request) {
        if (!this.client) {
            throw new Error('Cohere client not initialized. Please set API key first.');
        }
        try {
            const prompt = this.buildPrompt(request);
            const enhancedPrompt = `You are an expert programmer. Generate clean, efficient, and well-commented code based on the following requirements:\n\n${prompt}`;
            const response = await this.client.generate({
                model: 'command',
                prompt: enhancedPrompt,
                maxTokens: request.maxTokens || 1000,
                temperature: request.temperature || 0.3,
                stopSequences: ['```', '\n\n---']
            });
            return {
                content: response.generations[0]?.text?.trim() || '',
                model: request.model,
                usage: {
                    promptTokens: 0, // Cohere doesn't provide detailed token usage
                    completionTokens: 0,
                    totalTokens: 0
                },
                finishReason: 'stop'
            };
        }
        catch (error) {
            this.handleError(error);
        }
    }
}
exports.CohereProvider = CohereProvider;
//# sourceMappingURL=cohere.js.map